const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Home-CeG_pAz7.js","assets/vendor-DYfMlv50.js","assets/ui-BdXZoB_7.js","assets/Home-Re8rBuOp.css","assets/Login-C3BxX4y1.js","assets/Login-BGJ_U9wY.css","assets/Register-B0Rv5gQP.js","assets/Register-BhXb2ndU.css","assets/Levels-Da_21ZhS.js","assets/level-DWiiMk7C.js","assets/Levels-CFJoFy4u.css","assets/Game-QJROC0F-.js","assets/shop-bXOuIYZU.js","assets/Game-2vhGmJwg.css","assets/Shop-DtNBIdRe.js","assets/Shop-C9OJ065j.css","assets/Leaderboard-BpY9N58K.js","assets/social-CS2kFcht.js","assets/Leaderboard-CDsmBj6_.css","assets/Friends-CRSfqYYA.js","assets/Friends-BOkwiZs8.css","assets/Profile-DdHdpSws.js","assets/Profile-DajBlPJa.css"])))=>i.map(i=>d[i]);
var ut=Object.defineProperty;var lt=(e,t,n)=>t in e?ut(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Se=(e,t,n)=>lt(e,typeof t!="symbol"?t+"":t,n);import{ax as ft,r as ae,c as Re,ay as dt,x as pt,h as ht,y as mt,Q as yt,al as wt,z as bt,az as gt,aA as Et,au as St,aB as Rt}from"./vendor-DYfMlv50.js";import{E as K,a as Ot,i as At}from"./ui-BdXZoB_7.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();function Be(e,t){return function(){return e.apply(t,arguments)}}const{toString:Tt}=Object.prototype,{getPrototypeOf:we}=Object,{iterator:te,toStringTag:je}=Symbol,ne=(e=>t=>{const n=Tt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),P=e=>(e=e.toLowerCase(),t=>ne(t)===e),re=e=>t=>typeof t===e,{isArray:D}=Array,H=re("undefined");function M(e){return e!==null&&!H(e)&&e.constructor!==null&&!H(e.constructor)&&T(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const qe=P("ArrayBuffer");function _t(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&qe(e.buffer),t}const Pt=re("string"),T=re("function"),ve=re("number"),$=e=>e!==null&&typeof e=="object",Ct=e=>e===!0||e===!1,G=e=>{if(ne(e)!=="object")return!1;const t=we(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(je in e)&&!(te in e)},xt=e=>{if(!$(e)||M(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Nt=P("Date"),Lt=P("File"),Ut=P("Blob"),kt=P("FileList"),Ft=e=>$(e)&&T(e.pipe),It=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||T(e.append)&&((t=ne(e))==="formdata"||t==="object"&&T(e.toString)&&e.toString()==="[object FormData]"))},Dt=P("URLSearchParams"),[Bt,jt,qt,vt]=["ReadableStream","Request","Response","Headers"].map(P),Ht=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function z(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),D(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{if(M(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function He(e,t){if(M(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const k=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Me=e=>!H(e)&&e!==k;function fe(){const{caseless:e}=Me(this)&&this||{},t={},n=(r,s)=>{const o=e&&He(t,s)||s;G(t[o])&&G(r)?t[o]=fe(t[o],r):G(r)?t[o]=fe({},r):D(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&z(arguments[r],n);return t}const Mt=(e,t,n,{allOwnKeys:r}={})=>(z(t,(s,o)=>{n&&T(s)?e[o]=Be(s,n):e[o]=s},{allOwnKeys:r}),e),$t=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),zt=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Vt=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&we(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Jt=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Wt=e=>{if(!e)return null;if(D(e))return e;let t=e.length;if(!ve(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Kt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&we(Uint8Array)),Xt=(e,t)=>{const r=(e&&e[te]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Gt=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Qt=P("HTMLFormElement"),Zt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Oe=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Yt=P("RegExp"),$e=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};z(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},en=e=>{$e(e,(t,n)=>{if(T(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(T(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},tn=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return D(e)?r(e):r(String(e).split(t)),n},nn=()=>{},rn=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function sn(e){return!!(e&&T(e.append)&&e[je]==="FormData"&&e[te])}const on=e=>{const t=new Array(10),n=(r,s)=>{if($(r)){if(t.indexOf(r)>=0)return;if(M(r))return r;if(!("toJSON"in r)){t[s]=r;const o=D(r)?[]:{};return z(r,(i,c)=>{const f=n(i,s+1);!H(f)&&(o[c]=f)}),t[s]=void 0,o}}return r};return n(e,0)},an=P("AsyncFunction"),cn=e=>e&&($(e)||T(e))&&T(e.then)&&T(e.catch),ze=((e,t)=>e?setImmediate:t?((n,r)=>(k.addEventListener("message",({source:s,data:o})=>{s===k&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),k.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",T(k.postMessage)),un=typeof queueMicrotask<"u"?queueMicrotask.bind(k):typeof process<"u"&&process.nextTick||ze,ln=e=>e!=null&&T(e[te]),a={isArray:D,isArrayBuffer:qe,isBuffer:M,isFormData:It,isArrayBufferView:_t,isString:Pt,isNumber:ve,isBoolean:Ct,isObject:$,isPlainObject:G,isEmptyObject:xt,isReadableStream:Bt,isRequest:jt,isResponse:qt,isHeaders:vt,isUndefined:H,isDate:Nt,isFile:Lt,isBlob:Ut,isRegExp:Yt,isFunction:T,isStream:Ft,isURLSearchParams:Dt,isTypedArray:Kt,isFileList:kt,forEach:z,merge:fe,extend:Mt,trim:Ht,stripBOM:$t,inherits:zt,toFlatObject:Vt,kindOf:ne,kindOfTest:P,endsWith:Jt,toArray:Wt,forEachEntry:Xt,matchAll:Gt,isHTMLForm:Qt,hasOwnProperty:Oe,hasOwnProp:Oe,reduceDescriptors:$e,freezeMethods:en,toObjectSet:tn,toCamelCase:Zt,noop:nn,toFiniteNumber:rn,findKey:He,global:k,isContextDefined:Me,isSpecCompliantForm:sn,toJSONObject:on,isAsyncFn:an,isThenable:cn,setImmediate:ze,asap:un,isIterable:ln};function y(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ve=y.prototype,Je={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Je[e]={value:e}});Object.defineProperties(y,Je);Object.defineProperty(Ve,"isAxiosError",{value:!0});y.from=(e,t,n,r,s,o)=>{const i=Object.create(Ve);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),y.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const fn=null;function de(e){return a.isPlainObject(e)||a.isArray(e)}function We(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Ae(e,t,n){return e?e.concat(t).map(function(s,o){return s=We(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function dn(e){return a.isArray(e)&&!e.some(de)}const pn=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function se(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,h){return!a.isUndefined(h[b])});const r=n.metaTokens,s=n.visitor||l,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function u(p){if(p===null)return"";if(a.isDate(p))return p.toISOString();if(a.isBoolean(p))return p.toString();if(!f&&a.isBlob(p))throw new y("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(p)||a.isTypedArray(p)?f&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,b,h){let g=p;if(p&&!h&&typeof p=="object"){if(a.endsWith(b,"{}"))b=r?b:b.slice(0,-2),p=JSON.stringify(p);else if(a.isArray(p)&&dn(p)||(a.isFileList(p)||a.endsWith(b,"[]"))&&(g=a.toArray(p)))return b=We(b),g.forEach(function(R,x){!(a.isUndefined(R)||R===null)&&t.append(i===!0?Ae([b],x,o):i===null?b:b+"[]",u(R))}),!1}return de(p)?!0:(t.append(Ae(h,b,o),u(p)),!1)}const d=[],m=Object.assign(pn,{defaultVisitor:l,convertValue:u,isVisitable:de});function w(p,b){if(!a.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+b.join("."));d.push(p),a.forEach(p,function(g,S){(!(a.isUndefined(g)||g===null)&&s.call(t,g,a.isString(S)?S.trim():S,b,m))===!0&&w(g,b?b.concat(S):[S])}),d.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Te(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function be(e,t){this._pairs=[],e&&se(e,this,t)}const Ke=be.prototype;Ke.append=function(t,n){this._pairs.push([t,n])};Ke.toString=function(t){const n=t?function(r){return t.call(this,r,Te)}:Te;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function hn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Xe(e,t,n){if(!t)return e;const r=n&&n.encode||hn;a.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=a.isURLSearchParams(t)?t.toString():new be(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class _e{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ge={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},mn=typeof URLSearchParams<"u"?URLSearchParams:be,yn=typeof FormData<"u"?FormData:null,wn=typeof Blob<"u"?Blob:null,bn={isBrowser:!0,classes:{URLSearchParams:mn,FormData:yn,Blob:wn},protocols:["http","https","file","blob","url","data"]},ge=typeof window<"u"&&typeof document<"u",pe=typeof navigator=="object"&&navigator||void 0,gn=ge&&(!pe||["ReactNative","NativeScript","NS"].indexOf(pe.product)<0),En=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Sn=ge&&window.location.href||"http://localhost",Rn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ge,hasStandardBrowserEnv:gn,hasStandardBrowserWebWorkerEnv:En,navigator:pe,origin:Sn},Symbol.toStringTag,{value:"Module"})),O={...Rn,...bn};function On(e,t){return se(e,new O.classes.URLSearchParams,{visitor:function(n,r,s,o){return O.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function An(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tn(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Qe(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=n.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&a.isArray(s[i])&&(s[i]=Tn(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,s)=>{t(An(r),s,n,0)}),n}return null}function _n(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const V={transitional:Ge,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(Qe(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return On(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return se(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),_n(t)):t}],transformResponse:[function(t){const n=this.transitional||V.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?y.from(c,y.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:O.classes.FormData,Blob:O.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{V.headers[e]={}});const Pn=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Cn=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Pn[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Pe=Symbol("internals");function j(e){return e&&String(e).trim().toLowerCase()}function Q(e){return e===!1||e==null?e:a.isArray(e)?e.map(Q):String(e)}function xn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Nn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ce(e,t,n,r,s){if(a.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function Ln(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Un(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let _=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,f,u){const l=j(f);if(!l)throw new Error("header name must be a non-empty string");const d=a.findKey(s,l);(!d||s[d]===void 0||u===!0||u===void 0&&s[d]!==!1)&&(s[d||f]=Q(c))}const i=(c,f)=>a.forEach(c,(u,l)=>o(u,l,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!Nn(t))i(Cn(t),n);else if(a.isObject(t)&&a.isIterable(t)){let c={},f,u;for(const l of t){if(!a.isArray(l))throw TypeError("Object iterator must return a key-value pair");c[u=l[0]]=(f=c[u])?a.isArray(f)?[...f,l[1]]:[f,l[1]]:l[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=j(t),t){const r=a.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return xn(s);if(a.isFunction(n))return n.call(this,s,r);if(a.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=j(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ce(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=j(i),i){const c=a.findKey(r,i);c&&(!n||ce(r,r[c],c,n))&&(delete r[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||ce(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return a.forEach(this,(s,o)=>{const i=a.findKey(r,o);if(i){n[i]=Q(s),delete n[o];return}const c=t?Ln(o):String(o).trim();c!==o&&delete n[o],n[c]=Q(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Pe]=this[Pe]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=j(i);r[c]||(Un(s,i),r[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};_.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(_.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(_);function ue(e,t){const n=this||V,r=t||n,s=_.from(r.headers);let o=r.data;return a.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Ze(e){return!!(e&&e.__CANCEL__)}function B(e,t,n){y.call(this,e??"canceled",y.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(B,y,{__CANCEL__:!0});function Ye(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new y("Request failed with status code "+n.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function kn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Fn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const u=Date.now(),l=r[o];i||(i=u),n[s]=f,r[s]=u;let d=o,m=0;for(;d!==s;)m+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),u-i<t)return;const w=l&&u-l;return w?Math.round(m*1e3/w):void 0}}function In(e,t){let n=0,r=1e3/t,s,o;const i=(u,l=Date.now())=>{n=l,s=null,o&&(clearTimeout(o),o=null),e(...u)};return[(...u)=>{const l=Date.now(),d=l-n;d>=r?i(u,l):(s=u,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const Y=(e,t,n=3)=>{let r=0;const s=Fn(50,250);return In(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-r,u=s(f),l=i<=c;r=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:u||void 0,estimated:u&&c&&l?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(d)},n)},Ce=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},xe=e=>(...t)=>a.asap(()=>e(...t)),Dn=O.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,O.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(O.origin),O.navigator&&/(msie|trident)/i.test(O.navigator.userAgent)):()=>!0,Bn=O.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function jn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function qn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function et(e,t,n){let r=!jn(t);return e&&(r||n==!1)?qn(e,t):t}const Ne=e=>e instanceof _?{...e}:e;function I(e,t){t=t||{};const n={};function r(u,l,d,m){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:m},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function s(u,l,d,m){if(a.isUndefined(l)){if(!a.isUndefined(u))return r(void 0,u,d,m)}else return r(u,l,d,m)}function o(u,l){if(!a.isUndefined(l))return r(void 0,l)}function i(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return r(void 0,u)}else return r(void 0,l)}function c(u,l,d){if(d in t)return r(u,l);if(d in e)return r(void 0,u)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,l,d)=>s(Ne(u),Ne(l),d,!0)};return a.forEach(Object.keys({...e,...t}),function(l){const d=f[l]||s,m=d(e[l],t[l],l);a.isUndefined(m)&&d!==c||(n[l]=m)}),n}const tt=e=>{const t=I({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=_.from(i),t.url=Xe(et(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(O.hasStandardBrowserEnv||O.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[u,...l]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...l].join("; "))}}if(O.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&Dn(t.url))){const u=s&&o&&Bn.read(o);u&&i.set(s,u)}return t},vn=typeof XMLHttpRequest<"u",Hn=vn&&function(e){return new Promise(function(n,r){const s=tt(e);let o=s.data;const i=_.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:u}=s,l,d,m,w,p;function b(){w&&w(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let h=new XMLHttpRequest;h.open(s.method.toUpperCase(),s.url,!0),h.timeout=s.timeout;function g(){if(!h)return;const R=_.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),A={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:R,config:e,request:h};Ye(function(U){n(U),b()},function(U){r(U),b()},A),h=null}"onloadend"in h?h.onloadend=g:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(g)},h.onabort=function(){h&&(r(new y("Request aborted",y.ECONNABORTED,e,h)),h=null)},h.onerror=function(){r(new y("Network Error",y.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let x=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const A=s.transitional||Ge;s.timeoutErrorMessage&&(x=s.timeoutErrorMessage),r(new y(x,A.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,h)),h=null},o===void 0&&i.setContentType(null),"setRequestHeader"in h&&a.forEach(i.toJSON(),function(x,A){h.setRequestHeader(A,x)}),a.isUndefined(s.withCredentials)||(h.withCredentials=!!s.withCredentials),c&&c!=="json"&&(h.responseType=s.responseType),u&&([m,p]=Y(u,!0),h.addEventListener("progress",m)),f&&h.upload&&([d,w]=Y(f),h.upload.addEventListener("progress",d),h.upload.addEventListener("loadend",w)),(s.cancelToken||s.signal)&&(l=R=>{h&&(r(!R||R.type?new B(null,e,h):R),h.abort(),h=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const S=kn(s.url);if(S&&O.protocols.indexOf(S)===-1){r(new y("Unsupported protocol "+S+":",y.ERR_BAD_REQUEST,e));return}h.send(o||null)})},Mn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(u){if(!s){s=!0,c();const l=u instanceof Error?u:this.reason;r.abort(l instanceof y?l:new B(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,o(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:f}=r;return f.unsubscribe=()=>a.asap(c),f}},$n=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},zn=async function*(e,t){for await(const n of Vn(e))yield*$n(n,t)},Vn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Le=(e,t,n,r)=>{const s=zn(e,t);let o=0,i,c=f=>{i||(i=!0,r&&r(f))};return new ReadableStream({async pull(f){try{const{done:u,value:l}=await s.next();if(u){c(),f.close();return}let d=l.byteLength;if(n){let m=o+=d;n(m)}f.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},oe=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",nt=oe&&typeof ReadableStream=="function",Jn=oe&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),rt=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Wn=nt&&rt(()=>{let e=!1;const t=new Request(O.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ue=64*1024,he=nt&&rt(()=>a.isReadableStream(new Response("").body)),ee={stream:he&&(e=>e.body)};oe&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ee[t]&&(ee[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,r)})})})(new Response);const Kn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(O.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await Jn(e)).byteLength},Xn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??Kn(t)},Gn=oe&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:u,headers:l,withCredentials:d="same-origin",fetchOptions:m}=tt(e);u=u?(u+"").toLowerCase():"text";let w=Mn([s,o&&o.toAbortSignal()],i),p;const b=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let h;try{if(f&&Wn&&n!=="get"&&n!=="head"&&(h=await Xn(l,r))!==0){let A=new Request(t,{method:"POST",body:r,duplex:"half"}),L;if(a.isFormData(r)&&(L=A.headers.get("content-type"))&&l.setContentType(L),A.body){const[U,W]=Ce(h,Y(xe(f)));r=Le(A.body,Ue,U,W)}}a.isString(d)||(d=d?"include":"omit");const g="credentials"in Request.prototype;p=new Request(t,{...m,signal:w,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:g?d:void 0});let S=await fetch(p,m);const R=he&&(u==="stream"||u==="response");if(he&&(c||R&&b)){const A={};["status","statusText","headers"].forEach(Ee=>{A[Ee]=S[Ee]});const L=a.toFiniteNumber(S.headers.get("content-length")),[U,W]=c&&Ce(L,Y(xe(c),!0))||[];S=new Response(Le(S.body,Ue,U,()=>{W&&W(),b&&b()}),A)}u=u||"text";let x=await ee[a.findKey(ee,u)||"text"](S,e);return!R&&b&&b(),await new Promise((A,L)=>{Ye(A,L,{data:x,headers:_.from(S.headers),status:S.status,statusText:S.statusText,config:e,request:p})})}catch(g){throw b&&b(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,p),{cause:g.cause||g}):y.from(g,g&&g.code,e,p)}}),me={http:fn,xhr:Hn,fetch:Gn};a.forEach(me,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ke=e=>`- ${e}`,Qn=e=>a.isFunction(e)||e===null||e===!1,st={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Qn(n)&&(r=me[(i=String(n)).toLowerCase()],r===void 0))throw new y(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(ke).join(`
`):" "+ke(o[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:me};function le(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new B(null,e)}function Fe(e){return le(e),e.headers=_.from(e.headers),e.data=ue.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),st.getAdapter(e.adapter||V.adapter)(e).then(function(r){return le(e),r.data=ue.call(e,e.transformResponse,r),r.headers=_.from(r.headers),r},function(r){return Ze(r)||(le(e),r&&r.response&&(r.response.data=ue.call(e,e.transformResponse,r.response),r.response.headers=_.from(r.response.headers))),Promise.reject(r)})}const ot="1.11.0",ie={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ie[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ie={};ie.transitional=function(t,n,r){function s(o,i){return"[Axios v"+ot+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new y(s(i," has been removed"+(n?" in "+n:"")),y.ERR_DEPRECATED);return n&&!Ie[i]&&(Ie[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};ie.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Zn(e,t,n){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new y("option "+o+" must be "+f,y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new y("Unknown option "+o,y.ERR_BAD_OPTION)}}const Z={assertOptions:Zn,validators:ie},C=Z.validators;let F=class{constructor(t){this.defaults=t||{},this.interceptors={request:new _e,response:new _e}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=I(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&Z.assertOptions(r,{silentJSONParsing:C.transitional(C.boolean),forcedJSONParsing:C.transitional(C.boolean),clarifyTimeoutError:C.transitional(C.boolean)},!1),s!=null&&(a.isFunction(s)?n.paramsSerializer={serialize:s}:Z.assertOptions(s,{encode:C.function,serialize:C.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Z.assertOptions(n,{baseUrl:C.spelling("baseURL"),withXsrfToken:C.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=_.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(n)===!1||(f=f&&b.synchronous,c.unshift(b.fulfilled,b.rejected))});const u=[];this.interceptors.response.forEach(function(b){u.push(b.fulfilled,b.rejected)});let l,d=0,m;if(!f){const p=[Fe.bind(this),void 0];for(p.unshift(...c),p.push(...u),m=p.length,l=Promise.resolve(n);d<m;)l=l.then(p[d++],p[d++]);return l}m=c.length;let w=n;for(d=0;d<m;){const p=c[d++],b=c[d++];try{w=p(w)}catch(h){b.call(this,h);break}}try{l=Fe.call(this,w)}catch(p){return Promise.reject(p)}for(d=0,m=u.length;d<m;)l=l.then(u[d++],u[d++]);return l}getUri(t){t=I(this.defaults,t);const n=et(t.baseURL,t.url,t.allowAbsoluteUrls);return Xe(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){F.prototype[t]=function(n,r){return this.request(I(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(I(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}F.prototype[t]=n(),F.prototype[t+"Form"]=n(!0)});let Yn=class it{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new B(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new it(function(s){t=s}),cancel:t}}};function er(e){return function(n){return e.apply(null,n)}}function tr(e){return a.isObject(e)&&e.isAxiosError===!0}const ye={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ye).forEach(([e,t])=>{ye[t]=e});function at(e){const t=new F(e),n=Be(F.prototype.request,t);return a.extend(n,F.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return at(I(e,s))},n}const E=at(V);E.Axios=F;E.CanceledError=B;E.CancelToken=Yn;E.isCancel=Ze;E.VERSION=ot;E.toFormData=se;E.AxiosError=y;E.Cancel=E.CanceledError;E.all=function(t){return Promise.all(t)};E.spread=er;E.isAxiosError=tr;E.mergeConfig=I;E.AxiosHeaders=_;E.formToJSON=e=>Qe(a.isHTMLForm(e)?new FormData(e):e);E.getAdapter=st.getAdapter;E.HttpStatusCode=ye;E.default=E;const{Axios:hr,AxiosError:mr,CanceledError:yr,isCancel:wr,CancelToken:br,VERSION:gr,all:Er,Cancel:Sr,isAxiosError:Rr,spread:Or,toFormData:Ar,AxiosHeaders:Tr,HttpStatusCode:_r,formToJSON:Pr,getAdapter:Cr,mergeConfig:xr}=E;class nr{constructor(){Se(this,"instance");this.instance=E.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(t=>{const n=v();return n.token&&(t.headers.Authorization=`Bearer ${n.token}`),t},t=>Promise.reject(t)),this.instance.interceptors.response.use(t=>{const{code:n,message:r,data:s}=t.data;return n===200?{...t,data:s}:n===401?(v().logout(),K.error("登录已过期，请重新登录"),Promise.reject(new Error(r))):(K.error(r||"请求失败"),Promise.reject(new Error(r)))},t=>{var n;return((n=t.response)==null?void 0:n.status)===401?(v().logout(),K.error("登录已过期，请重新登录")):K.error(t.message||"网络错误"),Promise.reject(t)})}async get(t,n){return(await this.instance.get(t,{params:n})).data}async post(t,n){return(await this.instance.post(t,n)).data}async put(t,n){return(await this.instance.put(t,n)).data}async delete(t){return(await this.instance.delete(t)).data}}const q=new nr,X={login:e=>q.post("/user/login",e),register:e=>q.post("/user/register",e),getUserInfo:()=>q.get("/user/info"),checkUsername:e=>q.post("/user/check-username",null,{params:{username:e}}),recoverEnergy:()=>q.post("/user/recover-energy")},v=ft("user",()=>{const e=dt(),t=ae(""),n=ae(null),r=ae(!1),s=Re(()=>!!t.value&&!!n.value),o=Re(()=>n.value),i=async m=>{try{r.value=!0;const w=await X.login(m);return t.value=w.token,n.value=w.userInfo,localStorage.setItem("token",w.token),localStorage.setItem("userInfo",JSON.stringify(w.userInfo)),w}catch(w){throw w}finally{r.value=!1}},c=async m=>{try{r.value=!0;const w=await X.register(m);return t.value=w.token,n.value=w.userInfo,localStorage.setItem("token",w.token),localStorage.setItem("userInfo",JSON.stringify(w.userInfo)),w}catch(w){throw w}finally{r.value=!1}},f=()=>{t.value="",n.value=null,localStorage.removeItem("token"),localStorage.removeItem("userInfo"),e.push("/login")},u=()=>{const m=localStorage.getItem("token"),w=localStorage.getItem("userInfo");m&&w&&(t.value=m,n.value=JSON.parse(w))},l=async()=>{try{const m=await X.getUserInfo();n.value=m,localStorage.setItem("userInfo",JSON.stringify(m))}catch(m){console.error("更新用户信息失败:",m)}};return{token:t,userInfo:n,loading:r,isLoggedIn:s,currentUser:o,login:i,register:c,logout:f,checkLoginStatus:u,updateUserInfo:l,recoverEnergy:async()=>{try{await X.recoverEnergy(),await l()}catch(m){console.error("恢复体力失败:",m)}}}}),rr={id:"app"},sr=pt({__name:"App",setup(e){const t=v();return ht(()=>{t.checkLoginStatus()}),(n,r)=>{const s=wt("router-view");return bt(),mt("div",rr,[yt(s)])}}}),or=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},ir=or(sr,[["__scopeId","data-v-4c788989"]]),ar="modulepreload",cr=function(e){return"/"+e},De={},N=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),c=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));s=Promise.allSettled(n.map(f=>{if(f=cr(f),f in De)return;De[f]=!0;const u=f.endsWith(".css"),l=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${l}`))return;const d=document.createElement("link");if(d.rel=u?"stylesheet":ar,u||(d.as="script"),d.crossOrigin="",d.href=f,c&&d.setAttribute("nonce",c),document.head.appendChild(d),u)return new Promise((m,w)=>{d.addEventListener("load",m),d.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${f}`)))})}))}function o(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return s.then(i=>{for(const c of i||[])c.status==="rejected"&&o(c.reason);return t().catch(o)})},ct=gt({history:Et(),routes:[{path:"/",redirect:"/home"},{path:"/home",name:"Home",component:()=>N(()=>import("./Home-CeG_pAz7.js"),__vite__mapDeps([0,1,2,3])),meta:{requiresAuth:!0}},{path:"/login",name:"Login",component:()=>N(()=>import("./Login-C3BxX4y1.js"),__vite__mapDeps([4,1,2,5]))},{path:"/register",name:"Register",component:()=>N(()=>import("./Register-B0Rv5gQP.js"),__vite__mapDeps([6,1,2,7]))},{path:"/levels",name:"Levels",component:()=>N(()=>import("./Levels-Da_21ZhS.js"),__vite__mapDeps([8,1,2,9,10])),meta:{requiresAuth:!0}},{path:"/game/:levelId",name:"Game",component:()=>N(()=>import("./Game-QJROC0F-.js"),__vite__mapDeps([11,1,2,9,12,13])),meta:{requiresAuth:!0},props:!0},{path:"/shop",name:"Shop",component:()=>N(()=>import("./Shop-DtNBIdRe.js"),__vite__mapDeps([14,1,2,12,15])),meta:{requiresAuth:!0}},{path:"/leaderboard",name:"Leaderboard",component:()=>N(()=>import("./Leaderboard-BpY9N58K.js"),__vite__mapDeps([16,1,2,17,18])),meta:{requiresAuth:!0}},{path:"/friends",name:"Friends",component:()=>N(()=>import("./Friends-CRSfqYYA.js"),__vite__mapDeps([19,1,2,17,20])),meta:{requiresAuth:!0}},{path:"/profile",name:"Profile",component:()=>N(()=>import("./Profile-DdHdpSws.js"),__vite__mapDeps([21,1,2,17,22])),meta:{requiresAuth:!0}}]});ct.beforeEach((e,t,n)=>{const r=v();e.meta.requiresAuth&&!r.isLoggedIn?n("/login"):e.name==="Login"&&r.isLoggedIn?n("/home"):n()});const J=St(ir);for(const[e,t]of Object.entries(Ot))J.component(e,t);J.use(Rt());J.use(ct);J.use(At);J.mount("#app");export{or as _,X as a,q as b,v as u};
