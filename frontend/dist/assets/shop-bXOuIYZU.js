import{b as e}from"./index-zrXExFMB.js";const i={getUserItems:()=>e.get("/items/inventory"),getItemQuantity:t=>e.get(`/items/quantity/${t}`),useItem:t=>e.post("/items/use",t),checkItemAvailability:(t,s)=>e.post(`/items/check/${t}/${s}`)},n={getAllItems:()=>e.get("/shop/items"),getItemsByType:t=>e.get(`/shop/items/type/${t}`),getItemById:t=>e.get(`/shop/items/${t}`)},u={purchaseItem:t=>e.post("/purchase/buy",t),getPurchaseHistory:(t=20)=>e.get("/purchase/history",{limit:t}),getPurchaseByTransactionId:t=>e.get(`/purchase/transaction/${t}`)};export{i,u as p,n as s};
