import{x as H,r as b,c as D,h as K,y as i,A as s,Q as l,I as n,u as r,ay as O,al as _,O as t,H as v,K as z,P as A,a6 as B,M as m,D as N,z as o}from"./vendor-DYfMlv50.js";import{E as Q,k as j,m as q,t as J,b as W,y as T}from"./ui-BdXZoB_7.js";import{u as X,_ as Y}from"./index-zrXExFMB.js";import{l as h}from"./social-CS2kFcht.js";const Z={class:"leaderboard-page"},ss={class:"page-header"},es={class:"header-content"},as={class:"my-ranking"},ts={class:"leaderboard-tabs"},ls={class:"leaderboard-content"},os={key:0,class:"loading"},ns={key:1,class:"empty-state"},rs={key:2,class:"leaderboard-list"},ds={class:"top-three"},is={class:"rank-crown"},cs={class:"user-avatar"},us={class:"user-info"},_s={class:"score"},vs={class:"level"},ms={class:"rank-number"},ps={class:"rank-list"},fs={class:"rank-number"},ks={class:"user-avatar"},bs={class:"user-info"},gs={class:"user-score"},hs={class:"score"},ys={class:"personal-stats"},Cs={class:"stats-card"},zs={class:"stats-grid"},Us={class:"stat-item"},Ls={class:"stat-value"},Rs={class:"stat-item"},Fs={class:"stat-value"},ws={class:"stat-item"},Ss={class:"stat-value"},Vs={class:"stat-item"},Is={class:"stat-value"},Ms=H({__name:"Leaderboard",setup(xs){const U=O(),g=X(),c=b("global"),y=b(!0),p=b([]),C=b(null),L=b(null),E=D(()=>p.value.slice(0,3)),P=D(()=>p.value.slice(3)),R=async()=>{try{y.value=!0,c.value==="global"?p.value=await h.getGlobalLeaderboard(50):p.value=await h.getFriendsLeaderboard(20)}catch{Q.error("加载排行榜失败")}finally{y.value=!1}},$=async()=>{try{const[f,e]=await Promise.all([h.getMyRanking(),h.getMyFriendRanking()]);C.value=f,L.value=e}catch(f){console.error("加载排名失败:",f)}};return K(async()=>{await Promise.all([R(),$()])}),(f,e)=>{var I,M;const u=_("el-icon"),F=_("el-button"),w=_("el-tab-pane"),G=_("el-tabs"),S=_("el-avatar"),V=_("el-tag");return o(),i("div",Z,[s("div",ss,[s("div",es,[l(F,{onClick:e[0]||(e[0]=a=>r(U).back()),circle:""},{default:n(()=>[l(u,null,{default:n(()=>[l(r(j))]),_:1})]),_:1}),e[3]||(e[3]=s("h2",null,"排行榜",-1)),s("div",as,[s("span",null,"我的排名: #"+t(C.value||"--"),1)])])]),s("div",ts,[l(G,{modelValue:c.value,"onUpdate:modelValue":e[1]||(e[1]=a=>c.value=a),onTabChange:R},{default:n(()=>[l(w,{label:"全球排行",name:"global"}),l(w,{label:"好友排行",name:"friends"})]),_:1},8,["modelValue"])]),s("div",ls,[y.value?(o(),i("div",os,[l(u,{class:"spinner"},{default:n(()=>[l(r(q))]),_:1}),e[4]||(e[4]=s("p",null,"加载排行榜中...",-1))])):p.value.length===0?(o(),i("div",ns,[l(u,{size:"60",color:"#ccc"},{default:n(()=>[l(r(J))]),_:1}),s("p",null,t(c.value==="friends"?"还没有好友数据":"暂无排行榜数据"),1),c.value==="friends"?(o(),v(F,{key:0,onClick:e[2]||(e[2]=a=>r(U).push("/friends")),type:"primary"},{default:n(()=>e[5]||(e[5]=[m(" 添加好友 ",-1)])),_:1,__:[5]})):z("",!0)])):(o(),i("div",rs,[s("div",ds,[(o(!0),i(A,null,B(E.value,(a,d)=>(o(),i("div",{key:a.userId,class:N(["podium-item",`rank-${d+1}`])},[s("div",is,[d===0?(o(),v(u,{key:0,color:"#FFD700",size:"30"},{default:n(()=>[l(r(W))]),_:1})):d===1?(o(),v(u,{key:1,color:"#C0C0C0",size:"25"},{default:n(()=>[l(r(T))]),_:1})):(o(),v(u,{key:2,color:"#CD7F32",size:"20"},{default:n(()=>[l(r(T))]),_:1}))]),s("div",cs,[l(S,{size:d===0?60:50,src:a.avatarUrl},{default:n(()=>{var k;return[m(t(((k=a.nickname)==null?void 0:k.charAt(0))||"U"),1)]}),_:2},1032,["size","src"])]),s("div",us,[s("h4",null,t(a.nickname||a.username),1),s("p",_s,t(a.totalScore)+"分",1),s("p",vs,"第"+t(a.maxLevel)+"关",1)]),s("div",ms,t(d+1),1)],2))),128))]),s("div",ps,[(o(!0),i(A,null,B(P.value,a=>{var d,k;return o(),i("div",{key:a.userId,class:N(["rank-item",{"is-me":a.userId===((d=r(g).currentUser)==null?void 0:d.id)}])},[s("div",fs,t(a.rank),1),s("div",ks,[l(S,{size:45,src:a.avatarUrl},{default:n(()=>{var x;return[m(t(((x=a.nickname)==null?void 0:x.charAt(0))||"U"),1)]}),_:2},1032,["src"])]),s("div",bs,[s("h4",null,[m(t(a.nickname||a.username)+" ",1),a.isFriend&&c.value==="global"?(o(),v(V,{key:0,size:"small",type:"success"},{default:n(()=>e[6]||(e[6]=[m(" 好友 ",-1)])),_:1,__:[6]})):z("",!0),a.userId===((k=r(g).currentUser)==null?void 0:k.id)?(o(),v(V,{key:1,size:"small",type:"primary"},{default:n(()=>e[7]||(e[7]=[m(" 我 ",-1)])),_:1,__:[7]})):z("",!0)]),s("p",null,"关卡 "+t(a.maxLevel)+" • "+t(a.totalStars)+"⭐",1)]),s("div",gs,[s("span",hs,t(a.totalScore),1),e[8]||(e[8]=s("span",{class:"unit"},"分",-1))])],2)}),128))])]))]),s("div",ys,[s("div",Cs,[e[13]||(e[13]=s("h3",null,"我的成绩",-1)),s("div",zs,[s("div",Us,[e[9]||(e[9]=s("div",{class:"stat-label"},"总分",-1)),s("div",Ls,t(((I=r(g).currentUser)==null?void 0:I.totalScore)||0),1)]),s("div",Rs,[e[10]||(e[10]=s("div",{class:"stat-label"},"最高关卡",-1)),s("div",Fs,t(((M=r(g).currentUser)==null?void 0:M.maxLevel)||1),1)]),s("div",ws,[e[11]||(e[11]=s("div",{class:"stat-label"},"全球排名",-1)),s("div",Ss,"#"+t(C.value||"--"),1)]),s("div",Vs,[e[12]||(e[12]=s("div",{class:"stat-label"},"好友排名",-1)),s("div",Is,"#"+t(L.value||"--"),1)])])])])])}}}),Ts=Y(Ms,[["__scopeId","data-v-8d0742fc"]]);export{Ts as default};
