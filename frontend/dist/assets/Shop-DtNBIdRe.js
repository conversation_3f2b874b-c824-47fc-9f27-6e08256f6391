import{x as G,r as p,c as J,h as W,y as _,A as s,Q as a,I as o,u as m,ay as X,al as v,O as u,P as Y,a6 as Z,H as k,L as V,M as w,K as S,z as r}from"./vendor-DYfMlv50.js";import{E as $,k as ee,c as F,b as U,m as se,o as te,r as ae,j as le,l as oe,x as ne,e as ie}from"./ui-BdXZoB_7.js";import{u as re,_ as ue}from"./index-zrXExFMB.js";import{s as q,p as ce}from"./shop-bXOuIYZU.js";const de={class:"shop-page"},pe={class:"page-header"},_e={class:"header-content"},me={class:"user-currency"},ve={class:"currency-item"},fe={class:"currency-item"},ye={class:"shop-tabs"},he={class:"shop-content"},ge={key:0,class:"loading"},be={key:1,class:"items-grid"},Ie=["onClick"],ke={class:"item-icon"},Ve={class:"item-info"},we={class:"item-price"},Ce={class:"price-display"},Te={key:0,class:"purchase-content"},xe={class:"item-detail"},$e={class:"item-icon"},Fe={class:"item-info"},Ue={class:"purchase-options"},De={class:"quantity-selector"},Be={class:"total-price"},Ne={class:"price-display"},Pe={key:0,class:"insufficient-funds"},Me=G({__name:"Shop",setup(ze){const E=X(),y=re(),f=p("all"),C=p(!0),h=p(!1),T=p(!1),d=p(1),b=p([]),n=p(null),L=J(()=>f.value==="all"?b.value:b.value.filter(t=>t.itemType===f.value)),D=t=>({item_pack:ne,energy:oe,coins:F,diamonds:U,bomb:le,shuffle:ae,hint:te})[t]||ie,B=t=>t==="diamonds"?U:F,N=t=>t==="diamonds"?"#FF69B4":"#FFD700",Q=t=>t==="diamonds"?"钻石":"金币",P=t=>({item_pack:`包含${t.itemValue}个道具的礼包`,energy:`恢复${t.itemValue}点体力`,coins:`获得${t.itemValue}个金币`,diamonds:`获得${t.itemValue}个钻石`,bomb:"消除3x3区域的所有方块",shuffle:"重新随机排列棋盘",hint:"显示可消除的数字组合"})[t.itemType]||t.itemName,x=(t,e=1)=>{var c,I;const i=t.price*e;return(t.priceType==="diamonds"?((c=y.currentUser)==null?void 0:c.diamonds)||0:((I=y.currentUser)==null?void 0:I.coins)||0)>=i},R=t=>{n.value=t,d.value=1,h.value=!0},j=async()=>{if(n.value)try{T.value=!0;const t={shopItemId:n.value.id,quantity:d.value,paymentMethod:n.value.priceType},e=await ce.purchaseItem(t);await y.updateUserInfo();let i="购买成功！";e.rewardCoins&&(i+=` 获得${e.rewardCoins}金币`),e.rewardDiamonds&&(i+=` 获得${e.rewardDiamonds}钻石`),e.rewardItems&&(i+=` 获得${e.rewardItems}`),$.success(i),h.value=!1}catch(t){$.error(t.message||"购买失败")}finally{T.value=!1}},M=async()=>{try{C.value=!0,f.value==="all"?b.value=await q.getAllItems():b.value=await q.getItemsByType(f.value)}catch{$.error("加载商品失败")}finally{C.value=!1}};return W(()=>{M()}),(t,e)=>{var z,A;const i=v("el-icon"),g=v("el-button"),c=v("el-tab-pane"),I=v("el-tabs"),H=v("el-input-number"),K=v("el-alert"),O=v("el-dialog");return r(),_("div",de,[s("div",pe,[s("div",_e,[a(g,{onClick:e[0]||(e[0]=l=>m(E).back()),circle:""},{default:o(()=>[a(i,null,{default:o(()=>[a(m(ee))]),_:1})]),_:1}),e[5]||(e[5]=s("h2",null,"游戏商城",-1)),s("div",me,[s("div",ve,[a(i,{color:"#FFD700"},{default:o(()=>[a(m(F))]),_:1}),s("span",null,u(((z=m(y).currentUser)==null?void 0:z.coins)||0),1)]),s("div",fe,[a(i,{color:"#FF69B4"},{default:o(()=>[a(m(U))]),_:1}),s("span",null,u(((A=m(y).currentUser)==null?void 0:A.diamonds)||0),1)])])])]),s("div",ye,[a(I,{modelValue:f.value,"onUpdate:modelValue":e[1]||(e[1]=l=>f.value=l),onTabChange:M},{default:o(()=>[a(c,{label:"全部商品",name:"all"}),a(c,{label:"道具",name:"item_pack"}),a(c,{label:"体力",name:"energy"}),a(c,{label:"金币",name:"coins"}),a(c,{label:"钻石",name:"diamonds"})]),_:1},8,["modelValue"])]),s("div",he,[C.value?(r(),_("div",ge,[a(i,{class:"spinner"},{default:o(()=>[a(m(se))]),_:1}),e[6]||(e[6]=s("p",null,"加载商品中...",-1))])):(r(),_("div",be,[(r(!0),_(Y,null,Z(L.value,l=>(r(),_("div",{key:l.id,class:"item-card",onClick:Ae=>R(l)},[s("div",ke,[a(i,{size:"40"},{default:o(()=>[(r(),k(V(D(l.itemType))))]),_:2},1024)]),s("div",Ve,[s("h4",null,u(l.itemName),1),s("p",null,u(P(l)),1)]),s("div",we,[s("div",Ce,[a(i,{color:N(l.priceType)},{default:o(()=>[(r(),k(V(B(l.priceType))))]),_:2},1032,["color"]),s("span",null,u(l.price),1)]),a(g,{type:"primary",size:"small",disabled:!x(l)},{default:o(()=>e[7]||(e[7]=[w(" 购买 ",-1)])),_:2,__:[7]},1032,["disabled"])])],8,Ie))),128))]))]),a(O,{modelValue:h.value,"onUpdate:modelValue":e[4]||(e[4]=l=>h.value=l),title:"确认购买",width:"400px"},{footer:o(()=>[a(g,{onClick:e[3]||(e[3]=l=>h.value=!1)},{default:o(()=>e[10]||(e[10]=[w("取消",-1)])),_:1,__:[10]}),a(g,{type:"primary",onClick:j,loading:T.value,disabled:!n.value||!x(n.value,d.value)},{default:o(()=>e[11]||(e[11]=[w(" 确认购买 ",-1)])),_:1,__:[11]},8,["loading","disabled"])]),default:o(()=>[n.value?(r(),_("div",Te,[s("div",xe,[s("div",$e,[a(i,{size:"60"},{default:o(()=>[(r(),k(V(D(n.value.itemType))))]),_:1})]),s("div",Fe,[s("h3",null,u(n.value.itemName),1),s("p",null,u(P(n.value)),1)])]),s("div",Ue,[s("div",De,[e[8]||(e[8]=s("label",null,"购买数量:",-1)),a(H,{modelValue:d.value,"onUpdate:modelValue":e[2]||(e[2]=l=>d.value=l),min:1,max:10,size:"large"},null,8,["modelValue"])]),s("div",Be,[e[9]||(e[9]=s("span",null,"总价:",-1)),s("div",Ne,[a(i,{color:N(n.value.priceType)},{default:o(()=>[(r(),k(V(B(n.value.priceType))))]),_:1},8,["color"]),s("span",null,u(n.value.price*d.value),1)])])]),x(n.value,d.value)?S("",!0):(r(),_("div",Pe,[a(K,{title:"余额不足",type:"warning",closable:!1,"show-icon":""},{default:o(()=>[w(u(Q(n.value.priceType))+"不足，无法购买 ",1)]),_:1})]))])):S("",!0)]),_:1},8,["modelValue"])])}}}),Qe=ue(Me,[["__scopeId","data-v-198c342f"]]);export{Qe as default};
