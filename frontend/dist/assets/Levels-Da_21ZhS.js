import{x as I,r as m,c as x,h as F,y as a,A as e,Q as n,I as r,u as c,ay as K,al as C,O as i,P as p,a6 as k,K as f,M as N,z as o,D as y}from"./vendor-DYfMlv50.js";import{E as S,k as O,m as P,b as B,n as Q}from"./ui-BdXZoB_7.js";import{u as R,_ as j}from"./index-zrXExFMB.js";import{l as q}from"./level-DWiiMk7C.js";const G={class:"levels-page"},H={class:"page-header"},J={class:"header-content"},W={class:"header-stats"},X={class:"levels-content"},Y={key:0,class:"loading"},Z={key:1,class:"levels-grid"},ee=["onClick"],se={class:"level-number"},le={class:"stars"},te={class:"level-info"},ae={key:0},oe={key:1,class:"locked-text"},ne={key:0,class:"best-score"},ie={class:"difficulty"},ue={key:0,class:"level-detail"},re={class:"detail-header"},de={class:"stars"},ce={class:"detail-info"},ve={class:"info-item"},_e={class:"info-item"},fe={key:0,class:"info-item"},me={key:1,class:"info-item"},pe={class:"info-item"},ke={key:2,class:"info-item"},ye=I({__name:"Levels",setup(ge){const U=K(),V=R(),g=m(!0),b=m([]),v=m(!1),t=m(null),M=x(()=>{var u;return((u=V.currentUser)==null?void 0:u.maxLevel)||1}),$=x(()=>b.value.length),h=x(()=>{var u;return(((u=V.currentUser)==null?void 0:u.energy)||0)>0}),E=u=>({score:"达到分数",collect:"收集目标",special:"特殊挑战"})[u]||u,T=u=>{if(!u.isUnlocked){S.warning("该关卡尚未解锁");return}t.value=u,v.value=!0},A=()=>{if(t.value){if(!h.value){S.warning("体力不足，无法开始游戏");return}v.value=!1,U.push(`/game/${t.value.id}`)}},D=async()=>{try{g.value=!0,b.value=await q.getAllLevels()}catch{S.error("加载关卡失败")}finally{g.value=!1}};return F(()=>{D()}),(u,s)=>{var w;const _=C("el-icon"),L=C("el-button"),z=C("el-dialog");return o(),a("div",G,[e("div",H,[e("div",J,[n(L,{onClick:s[0]||(s[0]=l=>c(U).back()),circle:""},{default:r(()=>[n(_,null,{default:r(()=>[n(c(O))]),_:1})]),_:1}),s[3]||(s[3]=e("h2",null,"选择关卡",-1)),e("div",W,[e("span",null,"进度: "+i(M.value)+"/"+i($.value),1)])])]),e("div",X,[g.value?(o(),a("div",Y,[n(_,{class:"spinner"},{default:r(()=>[n(c(P))]),_:1}),s[4]||(s[4]=e("p",null,"加载关卡中...",-1))])):(o(),a("div",Z,[(o(!0),a(p,null,k(b.value,l=>(o(),a("div",{key:l.id,class:y(["level-card",{unlocked:l.isUnlocked,locked:!l.isUnlocked,completed:l.userStars>0}]),onClick:d=>T(l)},[e("div",se,i(l.levelNumber),1),e("div",le,[(o(),a(p,null,k(3,d=>n(_,{key:d,class:y({"star-filled":d<=l.userStars})},{default:r(()=>[n(c(B))]),_:2},1032,["class"])),64))]),e("div",te,[e("h4",null,i(l.levelName||`第${l.levelNumber}关`),1),l.isUnlocked?(o(),a("p",ae," 目标: "+i(l.targetValue)+"分 ",1)):(o(),a("p",oe,[n(_,null,{default:r(()=>[n(c(Q))]),_:1}),s[5]||(s[5]=N(" 未解锁 ",-1))]))]),l.userBestScore>0?(o(),a("div",ne," 最佳: "+i(l.userBestScore),1)):f("",!0),e("div",ie,[(o(),a(p,null,k(5,d=>e("div",{key:d,class:y(["difficulty-dot",{active:d<=l.difficulty}])},null,2)),64))])],10,ee))),128))]))]),n(z,{modelValue:v.value,"onUpdate:modelValue":s[2]||(s[2]=l=>v.value=l),title:`第${(w=t.value)==null?void 0:w.levelNumber}关`,width:"400px"},{footer:r(()=>{var l;return[n(L,{onClick:s[1]||(s[1]=d=>v.value=!1)},{default:r(()=>s[12]||(s[12]=[N("取消",-1)])),_:1,__:[12]}),n(L,{type:"primary",onClick:A,disabled:!((l=t.value)!=null&&l.isUnlocked)||!h.value},{default:r(()=>[N(i(h.value?"开始游戏":"体力不足"),1)]),_:1},8,["disabled"])]}),default:r(()=>[t.value?(o(),a("div",ue,[e("div",re,[e("h3",null,i(t.value.levelName||`第${t.value.levelNumber}关`),1),e("div",de,[(o(),a(p,null,k(3,l=>n(_,{key:l,class:y({"star-filled":l<=t.value.userStars})},{default:r(()=>[n(c(B))]),_:2},1032,["class"])),64))])]),e("div",ce,[e("div",ve,[s[6]||(s[6]=e("label",null,"目标类型:",-1)),e("span",null,i(E(t.value.targetType)),1)]),e("div",_e,[s[7]||(s[7]=e("label",null,"目标分数:",-1)),e("span",null,i(t.value.targetValue)+"分",1)]),t.value.maxMoves?(o(),a("div",fe,[s[8]||(s[8]=e("label",null,"步数限制:",-1)),e("span",null,i(t.value.maxMoves)+"步",1)])):f("",!0),t.value.timeLimit?(o(),a("div",me,[s[9]||(s[9]=e("label",null,"时间限制:",-1)),e("span",null,i(t.value.timeLimit)+"秒",1)])):f("",!0),e("div",pe,[s[10]||(s[10]=e("label",null,"通关奖励:",-1)),e("span",null,i(t.value.rewardCoins)+"金币",1)]),t.value.userBestScore>0?(o(),a("div",ke,[s[11]||(s[11]=e("label",null,"最佳成绩:",-1)),e("span",null,i(t.value.userBestScore)+"分",1)])):f("",!0)])])):f("",!0)]),_:1},8,["modelValue","title"])])}}}),Ce=j(ye,[["__scopeId","data-v-260b29dd"]]);export{Ce as default};
