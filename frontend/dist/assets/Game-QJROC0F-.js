var Me=Object.defineProperty;var Ee=(_,s,a)=>s in _?Me(_,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):_[s]=a;var D=(_,s,a)=>Ee(_,typeof s!="symbol"?s+"":s,a);import{ax as Ce,r as G,x as be,j as Ge,h as ke,S as we,y as L,A as d,n as Te,z,c as H,aC as ze,Q as b,I as B,al as le,O as C,K as x,P as ge,a6 as _e,ay as Ue,u as N,D as ye,H as J,L as Ae,M as Y}from"./vendor-DYfMlv50.js";import{j as Ie,r as Le,o as Pe,E as X,v as $e,p as Re,q as qe,t as xe,w as Ne,b as Ve,f as De}from"./ui-BdXZoB_7.js";import{b as K,_ as Be,u as He}from"./index-zrXExFMB.js";import{l as Ye}from"./level-DWiiMk7C.js";import{i as pe}from"./shop-bXOuIYZU.js";const Se={startGame:_=>K.post("/game/start",_),endGame:_=>K.post("/game/end",_),getGameSession:_=>K.get(`/game/session/${_}`),getGameHistory:(_=10)=>K.get("/game/history",{limit:_})},Xe=Ce("game",()=>{const _=G(null),s=G(null),a=G({sessionId:void 0,level:null,board:null,score:0,movesUsed:0,timeUsed:0,isGameActive:!1,selectedBlocks:[],itemsUsed:{}}),e=G(!1),i=async r=>{try{e.value=!0;const h=await Ye.getLevelInfo(r);_.value=h;const S={levelId:r},y=await Se.startGame(S);return s.value=y,a.value={sessionId:y.sessionId,level:h,board:f(),score:0,movesUsed:0,timeUsed:0,isGameActive:!0,selectedBlocks:[],itemsUsed:{}},y}catch(h){throw h}finally{e.value=!1}},u=async(r,h)=>{if(!s.value||!a.value.sessionId)throw new Error("游戏会话不存在");try{e.value=!0;const S={sessionId:a.value.sessionId,score:a.value.score,movesUsed:a.value.movesUsed,timeUsed:a.value.timeUsed,itemsUsed:JSON.stringify(a.value.itemsUsed),isVictory:r,endReason:h},y=await Se.endGame(S);return a.value.isGameActive=!1,y}catch(S){throw S}finally{e.value=!1}},f=()=>{const S=[];for(let y=0;y<8;y++){S[y]=[];for(let U=0;U<8;U++)S[y][U]={id:`${U}-${y}`,x:U,y,value:Math.floor(Math.random()*9)+1,isSelected:!1,canEliminate:!1}}return{width:8,height:8,blocks:S}},E=r=>{if(!a.value.isGameActive)return;const h=a.value.selectedBlocks;h.some(y=>y.id===r.id)?(a.value.selectedBlocks=h.filter(y=>y.id!==r.id),r.isSelected=!1):(h.push(r),r.isSelected=!0,v())},v=()=>{const r=a.value.selectedBlocks;r.length===2?r[0].value+r[1].value===10&&M(r[0],r[1])?T(r):k():r.length>2&&k()},M=(r,h)=>{var U,q;const S=r.y===h.y,y=r.x===h.x;if(!S&&!y)return!1;if(S){const $=Math.min(r.x,h.x),R=Math.max(r.x,h.x);for(let I=$+1;I<R;I++)if(((U=a.value.board)==null?void 0:U.blocks[r.y][I].value)!==0)return!1}else if(y){const $=Math.min(r.y,h.y),R=Math.max(r.y,h.y);for(let I=$+1;I<R;I++)if(((q=a.value.board)==null?void 0:q.blocks[I][r.x].value)!==0)return!1}return!0},T=r=>{r.forEach(h=>{a.value.board&&(a.value.board.blocks[h.y][h.x].value=0,a.value.board.blocks[h.y][h.x].isSelected=!1)}),a.value.score+=r.length*10,a.value.movesUsed+=1,a.value.selectedBlocks=[],A()},k=()=>{a.value.selectedBlocks.forEach(r=>{r.isSelected=!1}),a.value.selectedBlocks=[]},A=()=>{if(!a.value.board||!_.value)return;const r=a.value.movesUsed<(_.value.maxMoves||1/0),h=a.value.timeUsed<(_.value.timeLimit||1/0);a.value.score>=_.value.targetValue?u(!0,"target_reached"):r?h||u(!1,"time_up"):u(!1,"no_moves_left")};return{currentLevel:_,gameSession:s,gameState:a,loading:e,startGame:i,endGame:u,generateGameBoard:f,selectBlock:E,checkElimination:v,eliminateBlocks:T,clearSelection:k,checkGameEnd:A,resetGame:()=>{a.value={sessionId:void 0,level:null,board:null,score:0,movesUsed:0,timeUsed:0,isGameActive:!1,selectedBlocks:[],itemsUsed:{}},_.value=null,s.value=null}}});class Oe{constructor(s=8,a=8){D(this,"board");D(this,"selectedBlocks",[]);D(this,"score",0);D(this,"movesUsed",0);this.board=this.generateBoard(s,a)}generateBoard(s,a){const e=[];for(let i=0;i<a;i++){e[i]=[];for(let u=0;u<s;u++)e[i][u]={id:`${u}-${i}`,x:u,y:i,value:Math.floor(Math.random()*9)+1,isSelected:!1,canEliminate:!1,isAnimating:!1,position:{x:u*50+25,y:i*50+25}}}return{width:s,height:a,blocks:e}}selectBlock(s){return s.value===0||s.isAnimating?!1:this.selectedBlocks.some(e=>e.id===s.id)?(this.selectedBlocks=this.selectedBlocks.filter(e=>e.id!==s.id),s.isSelected=!1,this.clearEliminationHints(),!0):(this.selectedBlocks.length>=2&&this.clearSelection(),this.selectedBlocks.push(s),s.isSelected=!0,this.selectedBlocks.length===2&&(this.canEliminateBlocks(this.selectedBlocks[0],this.selectedBlocks[1])?(this.selectedBlocks.forEach(i=>i.canEliminate=!0),setTimeout(()=>this.eliminateSelectedBlocks(),500)):setTimeout(()=>this.clearSelection(),300)),!0)}canEliminateBlocks(s,a){if(s.value+a.value!==10)return!1;const e=s.y===a.y,i=s.x===a.x;return!e&&!i?!1:this.isPathClear(s,a)}isPathClear(s,a){if(s.y===a.y){const e=Math.min(s.x,a.x),i=Math.max(s.x,a.x);for(let u=e+1;u<i;u++)if(this.board.blocks[s.y][u].value!==0)return!1}else if(s.x===a.x){const e=Math.min(s.y,a.y),i=Math.max(s.y,a.y);for(let u=e+1;u<i;u++)if(this.board.blocks[u][s.x].value!==0)return!1}return!0}eliminateSelectedBlocks(){const s=[...this.selectedBlocks],a=s.length*10;return s.forEach(e=>{e.isAnimating=!0,setTimeout(()=>{e.value=0,e.isSelected=!1,e.canEliminate=!1,e.isAnimating=!1},300)}),this.score+=a,this.movesUsed+=1,this.selectedBlocks=[],setTimeout(()=>{this.applyGravity(),this.generateNewBlocks()},400),{eliminatedBlocks:s,score:a,newBlocks:[]}}applyGravity(){for(let s=0;s<this.board.width;s++){const a=[];for(let e=this.board.height-1;e>=0;e--)this.board.blocks[e][s].value!==0&&a.push(this.board.blocks[e][s]);for(let e=0;e<this.board.height;e++)this.board.blocks[e][s]={id:`${s}-${e}`,x:s,y:e,value:0,isSelected:!1,canEliminate:!1,isAnimating:!1,position:{x:s*50+25,y:e*50+25}};for(let e=0;e<a.length;e++){const i=this.board.height-1-e,u=a[e];u.y=i,u.id=`${s}-${i}`,u.position={x:s*50+25,y:i*50+25},this.board.blocks[i][s]=u}}}generateNewBlocks(){for(let s=0;s<this.board.width;s++)for(let a=0;a<this.board.height;a++)this.board.blocks[a][s].value===0&&(this.board.blocks[a][s]={id:`${s}-${a}`,x:s,y:a,value:Math.floor(Math.random()*9)+1,isSelected:!1,canEliminate:!1,isAnimating:!1,position:{x:s*50+25,y:a*50+25}})}clearSelection(){this.selectedBlocks.forEach(s=>{s.isSelected=!1,s.canEliminate=!1}),this.selectedBlocks=[]}clearEliminationHints(){for(let s=0;s<this.board.height;s++)for(let a=0;a<this.board.width;a++)this.board.blocks[s][a].canEliminate=!1}findPossibleMoves(){const s=[];for(let a=0;a<this.board.height;a++)for(let e=0;e<this.board.width;e++){const i=this.board.blocks[a][e];if(i.value===0)continue;const u=[[0,1],[1,0],[0,-1],[-1,0]];for(const[f,E]of u)for(let v=1;v<Math.max(this.board.width,this.board.height);v++){const M=e+f*v,T=a+E*v;if(M<0||M>=this.board.width||T<0||T>=this.board.height)break;const k=this.board.blocks[T][M];if(k.value!==0){i.value+k.value===10&&this.isPathClear(i,k)&&s.push([i,k]);break}}}return s}showHint(){const s=this.findPossibleMoves();return s.length===0?!1:(s[Math.floor(Math.random()*s.length)].forEach(e=>{e.canEliminate=!0}),setTimeout(()=>{this.clearEliminationHints()},3e3),!0)}shuffleBoard(){const s=[];for(let e=0;e<this.board.height;e++)for(let i=0;i<this.board.width;i++)this.board.blocks[e][i].value!==0&&s.push(this.board.blocks[e][i].value);for(let e=s.length-1;e>0;e--){const i=Math.floor(Math.random()*(e+1));[s[e],s[i]]=[s[i],s[e]]}let a=0;for(let e=0;e<this.board.height;e++)for(let i=0;i<this.board.width;i++)this.board.blocks[e][i].value!==0&&(this.board.blocks[e][i].value=s[a++]);this.clearSelection()}bombEffect(s,a){const e=[];for(let i=-1;i<=1;i++)for(let u=-1;u<=1;u++){const f=s+u,E=a+i;if(f>=0&&f<this.board.width&&E>=0&&E<this.board.height){const v=this.board.blocks[E][f];v.value!==0&&(e.push(v),v.isAnimating=!0,setTimeout(()=>{v.value=0,v.isSelected=!1,v.canEliminate=!1,v.isAnimating=!1},300))}}return setTimeout(()=>{this.applyGravity(),this.generateNewBlocks()},400),this.score+=e.length*15,this.movesUsed+=1,e}isGameOver(s,a){return s&&this.score>=s?{isOver:!0,reason:"target_reached"}:a&&this.movesUsed>=a?{isOver:!0,reason:"no_moves_left"}:this.findPossibleMoves().length===0?{isOver:!0,reason:"no_possible_moves"}:{isOver:!1}}getBoard(){return this.board}getScore(){return this.score}getMovesUsed(){return this.movesUsed}getSelectedBlocks(){return this.selectedBlocks}setScore(s){this.score=s}setMovesUsed(s){this.movesUsed=s}}const We={class:"game-canvas-container"},je=be({__name:"GameCanvas",props:{width:{default:8},height:{default:8},cellSize:{default:50},onScoreChange:{},onMovesChange:{},onGameEnd:{},targetScore:{default:1e3},maxMoves:{default:30}},emits:["scoreChange","movesChange","gameEnd","blockEliminated"],setup(_,{expose:s,emit:a}){const e=_,i=a,u=G(),f=G(),E=G(),v=e.width*e.cellSize,M=e.height*e.cellSize;let T=0,k=0;const A=()=>{f.value=new Oe(e.width,e.height),O()},O=()=>{const t=()=>{r(),j(),E.value=requestAnimationFrame(t)};t()},r=()=>{if(!u.value||!f.value)return;const l=u.value.getContext("2d");l&&(l.clearRect(0,0,v,M),h(l),S(l),q(l),$(l))},h=t=>{t.fillStyle="#f8f9fa",t.fillRect(0,0,v,M),t.strokeStyle="#e9ecef",t.lineWidth=1;for(let l=0;l<=e.width;l++)t.beginPath(),t.moveTo(l*e.cellSize,0),t.lineTo(l*e.cellSize,M),t.stroke();for(let l=0;l<=e.height;l++)t.beginPath(),t.moveTo(0,l*e.cellSize),t.lineTo(v,l*e.cellSize),t.stroke()},S=t=>{if(!f.value)return;const l=f.value.getBoard();for(let c=0;c<l.height;c++)for(let m=0;m<l.width;m++){const n=l.blocks[c][m];n.value!==0&&y(t,n)}},y=(t,l)=>{const c=l.x*e.cellSize,m=l.y*e.cellSize,n=e.cellSize-4,o=c+e.cellSize/2,g=m+e.cellSize/2;t.save();let p="#ffffff",P="#ddd";l.isSelected?(p="#667eea",P="#5a6fd8"):l.canEliminate&&(p="#ffeaa7",P="#fdcb6e"),U(t,c+2,m+2,n,n,8,p,P),t.fillStyle=l.isSelected?"#ffffff":"#333333",t.font=`bold ${e.cellSize*.6}px Arial`,t.textAlign="center",t.textBaseline="middle",t.fillText(l.value.toString(),o,g),l.isAnimating&&R(t,o,g),t.restore()},U=(t,l,c,m,n,o,g,p)=>{t.beginPath(),t.moveTo(l+o,c),t.lineTo(l+m-o,c),t.quadraticCurveTo(l+m,c,l+m,c+o),t.lineTo(l+m,c+n-o),t.quadraticCurveTo(l+m,c+n,l+m-o,c+n),t.lineTo(l+o,c+n),t.quadraticCurveTo(l,c+n,l,c+n-o),t.lineTo(l,c+o),t.quadraticCurveTo(l,c,l+o,c),t.closePath(),t.fillStyle=g,t.fill(),t.strokeStyle=p,t.lineWidth=2,t.stroke()},q=t=>{if(!f.value)return;const l=f.value.getSelectedBlocks();if(l.forEach(c=>{const m=c.x*e.cellSize+e.cellSize/2,n=c.y*e.cellSize+e.cellSize/2;t.save(),t.globalAlpha=.6,t.strokeStyle="#667eea",t.lineWidth=3,t.setLineDash([5,5]),t.beginPath(),t.arc(m,n,e.cellSize/2+5,0,Math.PI*2),t.stroke(),t.restore()}),l.length===2){const c=l[0],m=l[1];t.save(),t.strokeStyle="#667eea",t.lineWidth=3,t.globalAlpha=.8,t.beginPath(),t.moveTo(c.x*e.cellSize+e.cellSize/2,c.y*e.cellSize+e.cellSize/2),t.lineTo(m.x*e.cellSize+e.cellSize/2,m.y*e.cellSize+e.cellSize/2),t.stroke(),t.restore()}},$=t=>{if(!f.value)return;const l=f.value.getBoard(),c=Date.now()*.005;for(let m=0;m<l.height;m++)for(let n=0;n<l.width;n++){const o=l.blocks[m][n];if(o.canEliminate&&!o.isSelected){const g=o.x*e.cellSize+e.cellSize/2,p=o.y*e.cellSize+e.cellSize/2;t.save(),t.globalAlpha=(Math.sin(c*3)+1)*.3+.2,t.fillStyle="#ffd700",t.beginPath(),t.arc(g,p,e.cellSize/2+8,0,Math.PI*2),t.fill(),t.restore()}}},R=(t,l,c)=>{const m=Date.now()*.01;t.save(),t.globalAlpha=.8,t.fillStyle="#ff6b6b";for(let n=0;n<8;n++){const o=n/8*Math.PI*2+m,g=15+Math.sin(m*2)*5,p=l+Math.cos(o)*g,P=c+Math.sin(o)*g;t.beginPath(),t.arc(p,P,3,0,Math.PI*2),t.fill()}t.restore()},I=t=>{var n;if(!f.value)return;const l=(n=u.value)==null?void 0:n.getBoundingClientRect();if(!l)return;const c=Math.floor((t.clientX-l.left)/e.cellSize),m=Math.floor((t.clientY-l.top)/e.cellSize);W(c,m)},Q=t=>{var m;t.preventDefault();const l=t.touches[0];if(!l)return;const c=(m=u.value)==null?void 0:m.getBoundingClientRect();c&&(T=l.clientX-c.left,k=l.clientY-c.top)},Z=t=>{if(t.preventDefault(),!f.value)return;const l=Math.floor(T/e.cellSize),c=Math.floor(k/e.cellSize);W(l,c)},W=(t,l)=>{if(!f.value||t<0||t>=e.width||l<0||l>=e.height)return;const m=f.value.getBoard().blocks[l][t];f.value.selectBlock(m)&&(i("scoreChange",f.value.getScore()),i("movesChange",f.value.getMovesUsed()))},j=()=>{if(!f.value)return;const t=f.value.isGameOver(e.targetScore,e.maxMoves);if(t.isOver){const l=t.reason==="target_reached";i("gameEnd",l,t.reason||"unknown"),V()}},V=()=>{E.value&&(cancelAnimationFrame(E.value),E.value=void 0)},ee=()=>{var t;return((t=f.value)==null?void 0:t.showHint())||!1},te=()=>{var t;(t=f.value)==null||t.shuffleBoard()},se=(t,l)=>{if(!f.value)return[];const c=f.value.bombEffect(t,l);return i("blockEliminated",c),i("scoreChange",f.value.getScore()),i("movesChange",f.value.getMovesUsed()),c},F=()=>{V(),Te(()=>{A()})};return Ge(()=>e.targetScore,()=>{j()}),s({useHint:ee,shuffleBoard:te,useBomb:se,resetGame:F,getGameEngine:()=>f.value}),ke(()=>{A()}),we(()=>{V()}),(t,l)=>(z(),L("div",We,[d("canvas",{ref_key:"canvasRef",ref:u,width:v,height:M,onClick:I,onTouchstart:Q,onTouchend:Z,class:"game-canvas"},null,544)]))}}),Fe=Be(je,[["__scopeId","data-v-edad7edb"]]),Je={class:"game-page"},Ke={class:"game-header"},Qe={class:"header-left"},Ze={class:"level-info"},et={class:"header-center"},tt={class:"target-info"},st={class:"header-right"},at={class:"game-stats"},lt={key:0,class:"stat-item"},ot={key:1,class:"stat-item"},nt={class:"game-board-container"},it={class:"game-tools"},rt={class:"tools-container"},ct=["onClick"],ut={class:"tool-icon"},dt={class:"tool-info"},ft={class:"tool-name"},mt={class:"tool-count"},vt={class:"pause-content"},ht={class:"pause-stats"},gt={key:0},_t={class:"game-end-content"},yt={class:"result-icon"},pt={class:"result-stats"},St={class:"stat-row"},bt={class:"score"},kt={class:"stat-row"},wt={key:0,class:"stat-row"},Bt={class:"stars"},Mt={key:1,class:"stat-row"},Et={class:"reward"},Ct=be({__name:"Game",setup(_){const s=Ue(),a=ze(),e=Xe(),i=He(),u=G(),f=G(!1),E=G(!1),v=G(null),M=G(null),T=G([]),k=G(0),A=G(0),O=H(()=>parseInt(a.params.levelId)),r=H(()=>e.currentLevel),h=H(()=>{var o;return(((o=r.value)==null?void 0:o.maxMoves)||1/0)-A.value}),S=H(()=>{var o;const n=((o=r.value)==null?void 0:o.timeLimit)||1/0;return Math.max(0,n-e.gameState.timeUsed)}),y=H(()=>{var n,o,g;return[{type:"bomb",name:"炸弹",icon:Ie,quantity:((n=T.value.find(p=>p.itemType==="bomb"))==null?void 0:n.quantity)||0},{type:"shuffle",name:"重排",icon:Le,quantity:((o=T.value.find(p=>p.itemType==="shuffle"))==null?void 0:o.quantity)||0},{type:"hint",name:"提示",icon:Pe,quantity:((g=T.value.find(p=>p.itemType==="hint"))==null?void 0:g.quantity)||0}]}),U=n=>{const o=Math.floor(n/60),g=n%60;return`${o}:${g.toString().padStart(2,"0")}`},q=n=>{k.value=n,e.gameState.score=n},$=n=>{A.value=n,e.gameState.movesUsed=n},R=(n,o)=>{l(n,o)},I=n=>{console.log("消除了方块:",n)},Q=async n=>{const o=y.value.find(g=>g.type===n);if(!o||o.quantity<=0){X.warning(`${(o==null?void 0:o.name)||"道具"}数量不足`);return}try{await pe.useItem({itemType:n,quantity:1,sessionId:e.gameState.sessionId}),Z(n),await m(),X.success(`使用${o.name}成功`)}catch{X.error("道具使用失败")}},Z=n=>{if(u.value){switch(n){case"bomb":u.value.useBomb(4,4);break;case"shuffle":u.value.shuffleBoard();break;case"hint":u.value.useHint();break}e.gameState.itemsUsed[n]=(e.gameState.itemsUsed[n]||0)+1}},W=()=>{f.value=!0,t()},j=()=>{f.value=!1,F()},V=async()=>{try{await De.confirm("确定要退出游戏吗？进度将不会保存。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await e.endGame(!1,"user_exit"),s.push("/levels")}catch{}},ee=async()=>{E.value=!1,v.value=null,u.value&&u.value.resetGame(),await c()},te=()=>{var o;const n=(((o=r.value)==null?void 0:o.levelNumber)||1)+1;s.push(`/game/${n}`)},se=()=>{s.push("/levels")},F=()=>{var n;(n=r.value)!=null&&n.timeLimit&&!M.value&&(M.value=setInterval(()=>{e.gameState.isGameActive&&(e.gameState.timeUsed+=1,S.value<=0&&l(!1,"time_up"))},1e3))},t=()=>{M.value&&(clearInterval(M.value),M.value=null)},l=async(n,o)=>{t();try{const g=await e.endGame(n,o);v.value={isVictory:n,title:n?"恭喜通关！":"游戏结束",starsEarned:g.starsEarned,rewardCoins:g.rewardCoins},E.value=!0,await i.updateUserInfo()}catch{X.error("游戏结束处理失败")}},c=async()=>{try{await e.startGame(O.value),F(),k.value=0,A.value=0}catch(n){X.error(n.message||"游戏启动失败"),s.push("/levels")}},m=async()=>{try{T.value=await pe.getUserItems()}catch(n){console.error("加载道具失败:",n)}};return ke(async()=>{await m(),await c()}),we(()=>{t(),e.resetGame()}),(n,o)=>{var oe,ne,ie,re,ce,ue,de,fe;const g=le("el-icon"),p=le("el-button"),P=le("el-dialog");return z(),L("div",Je,[d("div",Ke,[d("div",Qe,[b(p,{onClick:W,circle:""},{default:B(()=>[b(g,null,{default:B(()=>[b(N($e))]),_:1})]),_:1}),d("div",Ze,[d("h3",null,"第"+C((oe=r.value)==null?void 0:oe.levelNumber)+"关",1),d("p",null,C((ne=r.value)==null?void 0:ne.levelName),1)])]),d("div",et,[d("div",tt,[d("span",null,"目标: "+C((ie=r.value)==null?void 0:ie.targetValue)+"分",1),d("span",null,"当前: "+C(k.value)+"分",1)])]),d("div",st,[d("div",at,[(re=r.value)!=null&&re.maxMoves?(z(),L("div",lt,[b(g,null,{default:B(()=>[b(N(Re))]),_:1}),d("span",null,C(h.value)+"/"+C(r.value.maxMoves),1)])):x("",!0),(ce=r.value)!=null&&ce.timeLimit?(z(),L("div",ot,[b(g,null,{default:B(()=>[b(N(qe))]),_:1}),d("span",null,C(U(S.value)),1)])):x("",!0)])])]),d("div",nt,[b(Fe,{ref_key:"gameCanvasRef",ref:u,width:8,height:8,cellSize:50,targetScore:((ue=r.value)==null?void 0:ue.targetValue)||1e3,maxMoves:((de=r.value)==null?void 0:de.maxMoves)||30,onScoreChange:q,onMovesChange:$,onGameEnd:R,onBlockEliminated:I},null,8,["targetScore","maxMoves"])]),d("div",it,[d("div",rt,[(z(!0),L(ge,null,_e(y.value,w=>(z(),L("div",{key:w.type,class:ye(["tool-item",{disabled:w.quantity<=0}]),onClick:ae=>Q(w.type)},[d("div",ut,[b(g,null,{default:B(()=>[(z(),J(Ae(w.icon)))]),_:2},1024)]),d("div",dt,[d("span",ft,C(w.name),1),d("span",mt,C(w.quantity),1)])],10,ct))),128))])]),b(P,{modelValue:f.value,"onUpdate:modelValue":o[0]||(o[0]=w=>f.value=w),title:"游戏暂停",width:"350px"},{footer:B(()=>[b(p,{onClick:j},{default:B(()=>o[2]||(o[2]=[Y("继续游戏",-1)])),_:1,__:[2]}),b(p,{onClick:V,type:"danger"},{default:B(()=>o[3]||(o[3]=[Y("退出游戏",-1)])),_:1,__:[3]})]),default:B(()=>{var w;return[d("div",vt,[o[1]||(o[1]=d("p",null,"游戏已暂停",-1)),d("div",ht,[d("div",null,"当前分数: "+C(k.value),1),d("div",null,"已用步数: "+C(A.value),1),(w=r.value)!=null&&w.timeLimit?(z(),L("div",gt," 剩余时间: "+C(U(S.value)),1)):x("",!0)])])]}),_:1},8,["modelValue"]),b(P,{v:"","-model":"showGameEndDialog",title:(fe=v.value)==null?void 0:fe.title,width:"400px"},{footer:B(()=>{var w;return[b(p,{onClick:ee},{default:B(()=>o[8]||(o[8]=[Y("重新开始",-1)])),_:1,__:[8]}),(w=v.value)!=null&&w.isVictory?(z(),J(p,{key:0,onClick:te,type:"primary"},{default:B(()=>o[9]||(o[9]=[Y(" 下一关 ",-1)])),_:1,__:[9]})):x("",!0),b(p,{onClick:se},{default:B(()=>o[10]||(o[10]=[Y("返回关卡",-1)])),_:1,__:[10]})]}),default:B(()=>{var w,ae,me;return[d("div",_t,[d("div",yt,[(w=v.value)!=null&&w.isVictory?(z(),J(g,{key:0,color:"#67c23a",size:"60"},{default:B(()=>[b(N(xe))]),_:1})):(z(),J(g,{key:1,color:"#f56c6c",size:"60"},{default:B(()=>[b(N(Ne))]),_:1}))]),d("div",pt,[d("div",St,[o[4]||(o[4]=d("span",null,"最终分数:",-1)),d("span",bt,C(k.value),1)]),d("div",kt,[o[5]||(o[5]=d("span",null,"使用步数:",-1)),d("span",null,C(A.value),1)]),(ae=v.value)!=null&&ae.isVictory?(z(),L("div",wt,[o[6]||(o[6]=d("span",null,"获得星级:",-1)),d("div",Bt,[(z(),L(ge,null,_e(3,ve=>{var he;return b(g,{key:ve,class:ye({"star-filled":ve<=(((he=v.value)==null?void 0:he.starsEarned)||0)})},{default:B(()=>[b(N(Ve))]),_:2},1032,["class"])}),64))])])):x("",!0),(me=v.value)!=null&&me.rewardCoins?(z(),L("div",Mt,[o[7]||(o[7]=d("span",null,"奖励金币:",-1)),d("span",Et,"+"+C(v.value.rewardCoins),1)])):x("",!0)])])]}),_:1},8,["title"])])}}}),Lt=Be(Ct,[["__scopeId","data-v-75203f40"]]);export{Lt as default};
