import{x as q,r as G,c as A,h as O,y as Q,A as e,Q as n,I as l,u as t,al as p,O as a,ay as R,z as j,M as m}from"./vendor-DYfMlv50.js";import{E as U,s as J,c as K,b as W,l as X,d as Y,g as Z,e as ee,t as se,u as te,f as ne}from"./ui-BdXZoB_7.js";import{u as re,_ as le}from"./index-zrXExFMB.js";const ae={class:"home-page"},oe={class:"top-bar"},ue={class:"user-info"},ie={class:"avatar"},ce={class:"user-details"},de={class:"settings"},_e={class:"resources"},pe={class:"resource-item"},me={class:"resource-item"},fe={class:"main-content"},ve={class:"start-game-section"},ge={class:"game-info"},Ue={class:"quick-actions"},ye={class:"action-grid"},ke={class:"profile-content"},xe={class:"profile-avatar"},he={class:"profile-info"},Ce=q({__name:"Home",setup(be){const c=R(),r=re(),f=G(!1),y=A(()=>{var d,u;const i=((d=r.currentUser)==null?void 0:d.energy)||0,s=((u=r.currentUser)==null?void 0:u.maxEnergy)||5;return i/s*100}),g=A(()=>{var i;return(((i=r.currentUser)==null?void 0:i.energy)||0)>0}),I=async()=>{y.value<100&&(await r.recoverEnergy(),U.info("体力恢复中..."))},N=()=>{var s;if(!g.value){U.warning("体力不足，无法开始游戏");return}const i=((s=r.currentUser)==null?void 0:s.maxLevel)||1;c.push(`/game/${i}`)},T=async()=>{try{await ne.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),r.logout(),U.success("已退出登录")}catch{}};return O(()=>{r.updateUserInfo()}),(i,s)=>{var k,x,h,C,b,w,E,B,$,L;const d=p("el-avatar"),u=p("el-icon"),v=p("el-button"),H=p("el-progress"),P=p("el-dialog");return j(),Q("div",ae,[e("div",oe,[e("div",ue,[e("div",ie,[n(d,{size:50,src:(k=t(r).currentUser)==null?void 0:k.avatarUrl},{default:l(()=>{var o,_;return[m(a(((_=(o=t(r).currentUser)==null?void 0:o.nickname)==null?void 0:_.charAt(0))||"U"),1)]}),_:1},8,["src"])]),e("div",ce,[e("h3",null,a(((x=t(r).currentUser)==null?void 0:x.nickname)||((h=t(r).currentUser)==null?void 0:h.username)),1),e("p",null,"关卡 "+a(((C=t(r).currentUser)==null?void 0:C.maxLevel)||1),1)])]),e("div",de,[n(v,{circle:"",onClick:s[0]||(s[0]=o=>f.value=!0)},{default:l(()=>[n(u,null,{default:l(()=>[n(t(J))]),_:1})]),_:1})])]),e("div",_e,[e("div",pe,[n(u,{color:"#FFD700"},{default:l(()=>[n(t(K))]),_:1}),e("span",null,a(((b=t(r).currentUser)==null?void 0:b.coins)||0),1)]),e("div",me,[n(u,{color:"#FF69B4"},{default:l(()=>[n(t(W))]),_:1}),e("span",null,a(((w=t(r).currentUser)==null?void 0:w.diamonds)||0),1)]),e("div",{class:"resource-item energy-item",onClick:I},[n(u,{color:"#32CD32"},{default:l(()=>[n(t(X))]),_:1}),e("span",null,a(((E=t(r).currentUser)==null?void 0:E.energy)||0)+"/"+a(((B=t(r).currentUser)==null?void 0:B.maxEnergy)||5),1),n(H,{percentage:y.value,"show-text":!1,"stroke-width":4,color:"#32CD32"},null,8,["percentage"])])]),e("div",fe,[s[11]||(s[11]=e("div",{class:"game-title"},[e("h1",null,"合十消"),e("p",null,"找到和为10的数字，消除它们！")],-1)),e("div",ve,[n(v,{type:"primary",size:"large",onClick:N,disabled:!g.value,class:"start-game-btn"},{default:l(()=>[n(u,null,{default:l(()=>[n(t(Y))]),_:1}),m(" "+a(g.value?"开始游戏":"体力不足"),1)]),_:1},8,["disabled"]),e("div",ge,[e("p",null,"当前进度：第 "+a((($=t(r).currentUser)==null?void 0:$.maxLevel)||1)+" 关",1),e("p",null,"总得分："+a(((L=t(r).currentUser)==null?void 0:L.totalScore)||0),1)])]),e("div",Ue,[e("div",ye,[e("div",{class:"action-item",onClick:s[1]||(s[1]=o=>t(c).push("/levels"))},[n(u,null,{default:l(()=>[n(t(Z))]),_:1}),s[7]||(s[7]=e("span",null,"选择关卡",-1))]),e("div",{class:"action-item",onClick:s[2]||(s[2]=o=>t(c).push("/shop"))},[n(u,null,{default:l(()=>[n(t(ee))]),_:1}),s[8]||(s[8]=e("span",null,"商城",-1))]),e("div",{class:"action-item",onClick:s[3]||(s[3]=o=>t(c).push("/leaderboard"))},[n(u,null,{default:l(()=>[n(t(se))]),_:1}),s[9]||(s[9]=e("span",null,"排行榜",-1))]),e("div",{class:"action-item",onClick:s[4]||(s[4]=o=>t(c).push("/friends"))},[n(u,null,{default:l(()=>[n(t(te))]),_:1}),s[10]||(s[10]=e("span",null,"好友",-1))])])])]),n(P,{modelValue:f.value,"onUpdate:modelValue":s[6]||(s[6]=o=>f.value=o),title:"个人资料",width:"400px"},{footer:l(()=>[n(v,{onClick:s[5]||(s[5]=o=>f.value=!1)},{default:l(()=>s[12]||(s[12]=[m("关闭",-1)])),_:1,__:[12]}),n(v,{type:"danger",onClick:T},{default:l(()=>s[13]||(s[13]=[m("退出登录",-1)])),_:1,__:[13]})]),default:l(()=>{var o,_,V,S,z,D;return[e("div",ke,[e("div",xe,[n(d,{size:80,src:(o=t(r).currentUser)==null?void 0:o.avatarUrl},{default:l(()=>{var F,M;return[m(a(((M=(F=t(r).currentUser)==null?void 0:F.nickname)==null?void 0:M.charAt(0))||"U"),1)]}),_:1},8,["src"])]),e("div",he,[e("h3",null,a(((_=t(r).currentUser)==null?void 0:_.nickname)||((V=t(r).currentUser)==null?void 0:V.username)),1),e("p",null,"用户名："+a((S=t(r).currentUser)==null?void 0:S.username),1),e("p",null,"最高关卡："+a(((z=t(r).currentUser)==null?void 0:z.maxLevel)||1),1),e("p",null,"总得分："+a(((D=t(r).currentUser)==null?void 0:D.totalScore)||0),1)])])]}),_:1},8,["modelValue"])])}}}),$e=le(Ce,[["__scopeId","data-v-ed909fb8"]]);export{$e as default};
