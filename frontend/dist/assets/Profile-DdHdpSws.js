import{x as W,r as g,X as N,h as Y,y as Z,A as t,Q as e,I as o,u as l,ay as tt,al as _,O as u,H as O,K as D,M as c,z as b}from"./vendor-DYfMlv50.js";import{k as st,t as et,g as lt,l as ot,c as nt,b as at,u as it,D as dt,F as rt,G as ut,H as ct,I as _t,J as vt,K as ft,f as L,E as k}from"./ui-BdXZoB_7.js";import{u as gt,_ as mt}from"./index-zrXExFMB.js";import{f as pt,l as P}from"./social-CS2kFcht.js";const bt={class:"profile-page"},kt={class:"page-header"},yt={class:"header-content"},Ut={class:"profile-content"},ht={class:"profile-card"},xt={class:"profile-header"},Et={class:"avatar-section"},St={class:"user-id"},Vt={class:"user-basic-info"},wt={class:"username"},Ct={class:"user-badges"},Bt={class:"game-stats"},Ft={class:"stats-grid"},At={class:"stat-item"},Rt={class:"stat-icon"},Tt={class:"stat-info"},It={class:"stat-value"},Mt={class:"stat-item"},Nt={class:"stat-icon"},Ot={class:"stat-info"},Dt={class:"stat-value"},Lt={class:"stat-item"},Pt={class:"stat-icon"},$t={class:"stat-info"},Jt={class:"stat-value"},jt={class:"stat-item"},zt={class:"stat-icon"},Ht={class:"stat-info"},Kt={class:"stat-value"},Gt={class:"stat-item"},Qt={class:"stat-icon"},Xt={class:"stat-info"},qt={class:"stat-value"},Wt={class:"stat-item"},Yt={class:"stat-icon"},Zt={class:"stat-info"},ts={class:"stat-value"},ss={class:"ranking-info"},es={class:"ranking-cards"},ls={class:"ranking-card"},os={class:"ranking-value"},ns={class:"ranking-card"},as={class:"ranking-value"},is={class:"settings-section"},ds={class:"settings-list"},rs={class:"setting-item"},us={class:"setting-info"},cs={class:"setting-item"},_s={class:"setting-info"},vs={class:"setting-item"},fs={class:"setting-info"},gs={class:"setting-item"},ms={class:"setting-info"},ps={class:"other-actions"},bs=W({__name:"Profile",setup(ks){const $=tt(),a=gt(),m=g(!1),y=g(0),U=g(null),h=g(null),d=N({soundEnabled:!0,musicEnabled:!0,vibrationEnabled:!0,notificationEnabled:!0}),J=async()=>{try{await L.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),a.logout(),k.success("已退出登录")}catch{}},j=async()=>{try{await L.confirm("确定要清除缓存吗？这将清除本地存储的游戏数据。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),localStorage.clear(),sessionStorage.clear(),k.success("缓存清除成功")}catch{}},z=()=>{var s,n;const r=`我在《合十消》游戏中已经通过第${(s=a.currentUser)==null?void 0:s.maxLevel}关，总得分${(n=a.currentUser)==null?void 0:n.totalScore}分！快来挑战我吧！`;navigator.share?navigator.share({title:"合十消 - 我的游戏成绩",text:r,url:window.location.origin}):(navigator.clipboard.writeText(r),k.success("个人资料已复制到剪贴板"))},H=async()=>{try{const[r,s,n]=await Promise.all([pt.getFriendCount(),P.getMyRanking(),P.getMyFriendRanking()]);y.value=r,U.value=s,h.value=n}catch(r){console.error("加载用户统计失败:",r)}},K=()=>{localStorage.setItem("gameSettings",JSON.stringify(d))},G=()=>{const r=localStorage.getItem("gameSettings");r&&Object.assign(d,JSON.parse(r))};Y(()=>{H(),G()});const Q=N(d);return Object.keys(d).forEach(r=>{Q[r]=()=>{K()}}),(r,s)=>{var x,E,S,V,w,C,B,F,A,R,T,I;const n=_("el-icon"),v=_("el-button"),X=_("el-avatar"),p=_("el-tag"),f=_("el-switch"),q=_("el-dialog");return b(),Z("div",bt,[t("div",kt,[t("div",yt,[e(v,{onClick:s[0]||(s[0]=i=>l($).back()),circle:""},{default:o(()=>[e(n,null,{default:o(()=>[e(l(st))]),_:1})]),_:1}),s[8]||(s[8]=t("h2",null,"个人资料",-1)),e(v,{onClick:J,type:"danger",plain:""},{default:o(()=>s[7]||(s[7]=[c(" 退出登录 ",-1)])),_:1,__:[7]})])]),t("div",Ut,[t("div",ht,[t("div",xt,[t("div",Et,[e(X,{size:100,src:(x=l(a).currentUser)==null?void 0:x.avatarUrl},{default:o(()=>{var i,M;return[c(u(((M=(i=l(a).currentUser)==null?void 0:i.nickname)==null?void 0:M.charAt(0))||"U"),1)]}),_:1},8,["src"]),t("div",St,"ID: "+u((E=l(a).currentUser)==null?void 0:E.id),1)]),t("div",Vt,[t("h2",null,u(((S=l(a).currentUser)==null?void 0:S.nickname)||((V=l(a).currentUser)==null?void 0:V.username)),1),t("p",wt,"@"+u((w=l(a).currentUser)==null?void 0:w.username),1),t("div",Ct,[e(p,{type:"primary"},{default:o(()=>s[9]||(s[9]=[c("玩家",-1)])),_:1,__:[9]}),(((C=l(a).currentUser)==null?void 0:C.maxLevel)||1)>=10?(b(),O(p,{key:0,type:"success"},{default:o(()=>s[10]||(s[10]=[c(" 进阶玩家 ",-1)])),_:1,__:[10]})):D("",!0),(((B=l(a).currentUser)==null?void 0:B.totalScore)||0)>=1e4?(b(),O(p,{key:1,type:"warning"},{default:o(()=>s[11]||(s[11]=[c(" 高分玩家 ",-1)])),_:1,__:[11]})):D("",!0)])])]),t("div",Bt,[s[18]||(s[18]=t("h3",null,"游戏统计",-1)),t("div",Ft,[t("div",At,[t("div",Rt,[e(n,{color:"#FFD700"},{default:o(()=>[e(l(et))]),_:1})]),t("div",Tt,[t("div",It,u(((F=l(a).currentUser)==null?void 0:F.totalScore)||0),1),s[12]||(s[12]=t("div",{class:"stat-label"},"总得分",-1))])]),t("div",Mt,[t("div",Nt,[e(n,{color:"#409EFF"},{default:o(()=>[e(l(lt))]),_:1})]),t("div",Ot,[t("div",Dt,u(((A=l(a).currentUser)==null?void 0:A.maxLevel)||1),1),s[13]||(s[13]=t("div",{class:"stat-label"},"最高关卡",-1))])]),t("div",Lt,[t("div",Pt,[e(n,{color:"#67C23A"},{default:o(()=>[e(l(ot))]),_:1})]),t("div",$t,[t("div",Jt,u(((R=l(a).currentUser)==null?void 0:R.energy)||0),1),s[14]||(s[14]=t("div",{class:"stat-label"},"当前体力",-1))])]),t("div",jt,[t("div",zt,[e(n,{color:"#E6A23C"},{default:o(()=>[e(l(nt))]),_:1})]),t("div",Ht,[t("div",Kt,u(((T=l(a).currentUser)==null?void 0:T.coins)||0),1),s[15]||(s[15]=t("div",{class:"stat-label"},"金币",-1))])]),t("div",Gt,[t("div",Qt,[e(n,{color:"#F56C6C"},{default:o(()=>[e(l(at))]),_:1})]),t("div",Xt,[t("div",qt,u(((I=l(a).currentUser)==null?void 0:I.diamonds)||0),1),s[16]||(s[16]=t("div",{class:"stat-label"},"钻石",-1))])]),t("div",Wt,[t("div",Yt,[e(n,{color:"#909399"},{default:o(()=>[e(l(it))]),_:1})]),t("div",Zt,[t("div",ts,u(y.value),1),s[17]||(s[17]=t("div",{class:"stat-label"},"好友数",-1))])])])]),t("div",ss,[s[23]||(s[23]=t("h3",null,"排名信息",-1)),t("div",es,[t("div",ls,[s[19]||(s[19]=t("div",{class:"ranking-title"},"全球排名",-1)),t("div",os,"#"+u(U.value||"--"),1),s[20]||(s[20]=t("div",{class:"ranking-desc"},"在所有玩家中的排名",-1))]),t("div",ns,[s[21]||(s[21]=t("div",{class:"ranking-title"},"好友排名",-1)),t("div",as,"#"+u(h.value||"--"),1),s[22]||(s[22]=t("div",{class:"ranking-desc"},"在好友中的排名",-1))])])]),t("div",is,[s[28]||(s[28]=t("h3",null,"设置",-1)),t("div",ds,[t("div",rs,[t("div",us,[e(n,null,{default:o(()=>[e(l(dt))]),_:1}),s[24]||(s[24]=t("span",null,"声音效果",-1))]),e(f,{modelValue:d.soundEnabled,"onUpdate:modelValue":s[1]||(s[1]=i=>d.soundEnabled=i)},null,8,["modelValue"])]),t("div",cs,[t("div",_s,[e(n,null,{default:o(()=>[e(l(rt))]),_:1}),s[25]||(s[25]=t("span",null,"背景音乐",-1))]),e(f,{modelValue:d.musicEnabled,"onUpdate:modelValue":s[2]||(s[2]=i=>d.musicEnabled=i)},null,8,["modelValue"])]),t("div",vs,[t("div",fs,[e(n,null,{default:o(()=>[e(l(ut))]),_:1}),s[26]||(s[26]=t("span",null,"震动反馈",-1))]),e(f,{modelValue:d.vibrationEnabled,"onUpdate:modelValue":s[3]||(s[3]=i=>d.vibrationEnabled=i)},null,8,["modelValue"])]),t("div",gs,[t("div",ms,[e(n,null,{default:o(()=>[e(l(ct))]),_:1}),s[27]||(s[27]=t("span",null,"推送通知",-1))]),e(f,{modelValue:d.notificationEnabled,"onUpdate:modelValue":s[4]||(s[4]=i=>d.notificationEnabled=i)},null,8,["modelValue"])])])]),t("div",ps,[e(v,{onClick:s[5]||(s[5]=i=>m.value=!0),text:""},{default:o(()=>[e(n,null,{default:o(()=>[e(l(_t))]),_:1}),s[29]||(s[29]=c(" 关于游戏 ",-1))]),_:1,__:[29]}),e(v,{onClick:j,text:""},{default:o(()=>[e(n,null,{default:o(()=>[e(l(vt))]),_:1}),s[30]||(s[30]=c(" 清除缓存 ",-1))]),_:1,__:[30]}),e(v,{onClick:z,type:"primary",text:""},{default:o(()=>[e(n,null,{default:o(()=>[e(l(ft))]),_:1}),s[31]||(s[31]=c(" 分享资料 ",-1))]),_:1,__:[31]})])])]),e(q,{modelValue:m.value,"onUpdate:modelValue":s[6]||(s[6]=i=>m.value=i),title:"关于合十消",width:"400px"},{default:o(()=>s[32]||(s[32]=[t("div",{class:"about-content"},[t("div",{class:"about-logo"},[t("h2",null,"合十消"),t("p",null,"v1.0.0")]),t("div",{class:"about-desc"},[t("p",null,"《合十消》是一款创新的数字消除类益智游戏。通过寻找和为10的数字组合进行消除，锻炼你的数学思维和策略能力。")]),t("div",{class:"about-features"},[t("h4",null,"游戏特色"),t("ul",null,[t("li",null,'创新的"和为10"消除玩法'),t("li",null,"丰富的关卡挑战"),t("li",null,"多样化的道具系统"),t("li",null,"好友社交互动"),t("li",null,"全球排行榜竞技")])]),t("div",{class:"about-footer"},[t("p",null,"© 2025 合十消游戏工作室")])],-1)])),_:1,__:[32]},8,["modelValue"])])}}}),Es=mt(bs,[["__scopeId","data-v-34f7a62b"]]);export{Es as default};
