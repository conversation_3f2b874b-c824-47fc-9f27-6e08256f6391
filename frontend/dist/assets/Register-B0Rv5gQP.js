import{x as R,r as p,X as B,j as A,y as V,A as t,Q as s,I as a,V as F,al as u,M as _,K as N,H as y,u as x,D as S,O as q,ay as D,z as f}from"./vendor-DYfMlv50.js";import{h as M,j,E as v}from"./ui-BdXZoB_7.js";import{u as I,a as L,_ as H}from"./index-zrXExFMB.js";const K={class:"register-page"},O={class:"register-container"},Q={class:"register-card"},T={key:0,class:"username-status"},X={class:"register-footer"},G=R({__name:"Register",setup(J){const b=D(),h=I(),d=p(),g=p(!1),c=p(!1),n=p(!1),r=B({username:"",password:"",nickname:"",confirmPassword:""}),P={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度应为3-20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度应为6-20个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(i,e,o)=>{e?e!==r.password?o(new Error("两次密码输入不一致")):o():o(new Error("请确认密码"))},trigger:"blur"}],nickname:[{max:50,message:"昵称长度不能超过50个字符",trigger:"blur"}]},C=async()=>{if(!r.username||r.username.length<3){c.value=!1;return}try{n.value=await L.checkUsername(r.username),c.value=!0}catch{c.value=!1}},w=async()=>{if(d.value)try{if(await d.value.validate(),!n.value){v.error("用户名不可用");return}g.value=!0;const{confirmPassword:i,...e}=r;await h.register(e),v.success("注册成功！"),b.push("/home")}catch(i){i instanceof Error&&v.error(i.message)}finally{g.value=!1}};return A(()=>r.password,()=>{r.confirmPassword&&d.value&&d.value.validateField("confirmPassword")}),(i,e)=>{const o=u("el-input"),k=u("el-icon"),m=u("el-form-item"),U=u("el-button"),z=u("el-form"),E=u("router-link");return f(),V("div",K,[t("div",O,[t("div",Q,[e[7]||(e[7]=t("div",{class:"register-header"},[t("h1",null,"注册账号"),t("p",null,"加入合十消，开启数字消除之旅")],-1)),s(z,{model:r,rules:P,ref_key:"registerFormRef",ref:d,onSubmit:F(w,["prevent"])},{default:a(()=>[s(m,{prop:"username"},{default:a(()=>[s(o,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=l=>r.username=l),placeholder:"请输入用户名",size:"large","prefix-icon":"User",onBlur:C},null,8,["modelValue"]),c.value?(f(),V("div",T,[n.value?(f(),y(k,{key:0,color:"#67c23a"},{default:a(()=>[s(x(M))]),_:1})):(f(),y(k,{key:1,color:"#f56c6c"},{default:a(()=>[s(x(j))]),_:1})),t("span",{class:S(n.value?"text-success":"text-error")},q(n.value?"用户名可用":"用户名已被使用"),3)])):N("",!0)]),_:1}),s(m,{prop:"password"},{default:a(()=>[s(o,{modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=l=>r.password=l),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),s(m,{prop:"confirmPassword"},{default:a(()=>[s(o,{modelValue:r.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=l=>r.confirmPassword=l),type:"password",placeholder:"请确认密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),s(m,{prop:"nickname"},{default:a(()=>[s(o,{modelValue:r.nickname,"onUpdate:modelValue":e[3]||(e[3]=l=>r.nickname=l),placeholder:"请输入昵称（可选）",size:"large","prefix-icon":"Avatar"},null,8,["modelValue"])]),_:1}),s(m,null,{default:a(()=>[s(U,{type:"primary",size:"large",loading:g.value,disabled:!n.value,onClick:w,class:"register-btn"},{default:a(()=>e[4]||(e[4]=[_(" 注册 ",-1)])),_:1,__:[4]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"]),t("div",X,[t("p",null,[e[6]||(e[6]=_("已有账号？ ",-1)),s(E,{to:"/login",class:"login-link"},{default:a(()=>e[5]||(e[5]=[_(" 立即登录 ",-1)])),_:1,__:[5]})])])])])])}}}),ee=H(G,[["__scopeId","data-v-65f46c7b"]]);export{ee as default};
