import{x as b,r as _,X as k,y as h,A as s,Q as o,I as t,V as z,al as l,M as p,a4 as C,ay as E,z as L}from"./vendor-DYfMlv50.js";import{u as U,_ as B}from"./index-zrXExFMB.js";import{E as c}from"./ui-BdXZoB_7.js";const F={class:"login-page"},M={class:"login-container"},N={class:"login-card"},R={class:"login-footer"},S=b({__name:"Login",setup(q){const f=E(),v=U(),n=_(),a=_(!1),r=k({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度应为3-20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度应为6-20个字符",trigger:"blur"}]},i=async()=>{if(n.value)try{await n.value.validate(),a.value=!0,await v.login(r),c.success("登录成功！"),f.push("/home")}catch(u){u instanceof Error&&c.error(u.message)}finally{a.value=!1}};return(u,e)=>{const g=l("el-input"),d=l("el-form-item"),y=l("el-button"),x=l("el-form"),V=l("router-link");return L(),h("div",F,[s("div",M,[s("div",N,[e[5]||(e[5]=s("div",{class:"login-header"},[s("h1",null,"合十消"),s("p",null,"数字消除益智游戏")],-1)),o(x,{model:r,rules:w,ref_key:"loginFormRef",ref:n,onSubmit:z(i,["prevent"])},{default:t(()=>[o(d,{prop:"username"},{default:t(()=>[o(g,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=m=>r.username=m),placeholder:"请输入用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),o(d,{prop:"password"},{default:t(()=>[o(g,{modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=m=>r.password=m),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:C(i,["enter"])},null,8,["modelValue"])]),_:1}),o(d,null,{default:t(()=>[o(y,{type:"primary",size:"large",loading:a.value,onClick:i,class:"login-btn"},{default:t(()=>e[2]||(e[2]=[p(" 登录 ",-1)])),_:1,__:[2]},8,["loading"])]),_:1})]),_:1},8,["model"]),s("div",R,[s("p",null,[e[4]||(e[4]=p("还没有账号？ ",-1)),o(V,{to:"/register",class:"register-link"},{default:t(()=>e[3]||(e[3]=[p(" 立即注册 ",-1)])),_:1,__:[3]})])])])])])}}}),Q=B(S,[["__scopeId","data-v-573b1ce0"]]);export{Q as default};
