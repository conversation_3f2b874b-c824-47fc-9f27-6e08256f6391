import{x as Q,r as f,X,h as j,y as c,A as l,Q as t,I as s,u as i,ay as G,al as m,O as a,P as H,a6 as J,M as o,D as W,K as Y,z as v}from"./vendor-DYfMlv50.js";import{E as y,k as Z,m as ee,z as le,A as te,t as se,b as ne,y as ae,B as oe,C as de,f as re}from"./ui-BdXZoB_7.js";import{u as ie,_ as ue}from"./index-zrXExFMB.js";import{f as w}from"./social-CS2kFcht.js";const _e={class:"friends-page"},me={class:"page-header"},fe={class:"header-content"},ce={class:"friend-count"},ve={class:"friends-content"},pe={key:0,class:"loading"},ge={key:1,class:"empty-state"},ye={key:2,class:"friends-list"},ke={class:"list-header"},Ie={class:"friend-cards"},we={class:"friend-avatar"},Ce={class:"friend-info"},Fe={class:"username"},xe={class:"friend-stats"},Ve={class:"stat-item"},be={class:"stat-item"},he={class:"stat-item"},De={class:"friend-actions"},Ue={class:"ranking"},ze={class:"rank-number"},Se={class:"action-buttons"},$e={class:"add-friend-content"},Ae={key:0,class:"challenge-content"},Be={class:"challenge-header"},Le={class:"challenge-info"},Me={class:"vs-stats"},Ee={class:"player-stat"},Ne={class:"player-stat"},Re=Q({__name:"Friends",setup(Te){const $=G(),h=ie(),C=f(!0),F=f([]),D=f(0),p=f(!1),k=f(!1),x=f(!1),_=f(null),V=f(),I=X({friendId:""}),A={friendId:[{required:!0,message:"请输入用户ID",trigger:"blur"},{pattern:/^\d+$/,message:"用户ID必须是数字",trigger:"blur"}]},B=d=>Math.random()>.5,L=d=>{_.value=d,k.value=!0},M=async d=>{const[e,u]=d.split("-");if(e==="remove")try{await re.confirm("确定要删除这个好友吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await w.removeFriend(parseInt(u)),y.success("好友删除成功"),await b()}catch(r){r!=="cancel"&&y.error(r.message||"删除好友失败")}},E=async()=>{if(V.value)try{await V.value.validate(),x.value=!0;const d=parseInt(I.friendId);await w.addFriend(d),y.success("好友添加成功"),p.value=!1,I.friendId="",await b()}catch(d){y.error(d.message||"添加好友失败")}finally{x.value=!1}},N=()=>{y.info("挑战功能开发中，敬请期待！"),k.value=!1},b=async()=>{try{C.value=!0;const[d,e]=await Promise.all([w.getFriends(),w.getFriendCount()]);F.value=d,D.value=e}catch{y.error("加载好友列表失败")}finally{C.value=!1}};return j(()=>{b()}),(d,e)=>{const u=m("el-icon"),r=m("el-button"),U=m("el-avatar"),R=m("el-dropdown-item"),T=m("el-dropdown-menu"),O=m("el-dropdown"),P=m("el-input"),q=m("el-form-item"),K=m("el-form"),z=m("el-dialog");return v(),c("div",_e,[l("div",me,[l("div",fe,[t(r,{onClick:e[0]||(e[0]=n=>i($).back()),circle:""},{default:s(()=>[t(u,null,{default:s(()=>[t(i(Z))]),_:1})]),_:1}),e[8]||(e[8]=l("h2",null,"好友",-1)),l("div",ce,[l("span",null,a(D.value)+"个好友",1)])])]),l("div",ve,[C.value?(v(),c("div",pe,[t(u,{class:"spinner"},{default:s(()=>[t(i(ee))]),_:1}),e[9]||(e[9]=l("p",null,"加载好友列表中...",-1))])):F.value.length===0?(v(),c("div",ge,[t(u,{size:"60",color:"#ccc"},{default:s(()=>[t(i(le))]),_:1}),e[11]||(e[11]=l("h3",null,"还没有好友",-1)),e[12]||(e[12]=l("p",null,"添加好友一起玩游戏吧！",-1)),t(r,{onClick:e[1]||(e[1]=n=>p.value=!0),type:"primary",size:"large"},{default:s(()=>e[10]||(e[10]=[o(" 添加好友 ",-1)])),_:1,__:[10]})])):(v(),c("div",ye,[l("div",ke,[e[14]||(e[14]=l("h3",null,"好友列表",-1)),t(r,{onClick:e[2]||(e[2]=n=>p.value=!0),type:"primary"},{default:s(()=>[t(u,null,{default:s(()=>[t(i(te))]),_:1}),e[13]||(e[13]=o(" 添加好友 ",-1))]),_:1,__:[13]})]),l("div",Ie,[(v(!0),c(H,null,J(F.value,n=>(v(),c("div",{key:n.userId,class:"friend-card"},[l("div",we,[t(U,{size:60,src:n.avatarUrl},{default:s(()=>{var g;return[o(a(((g=n.nickname)==null?void 0:g.charAt(0))||"U"),1)]}),_:2},1032,["src"]),l("div",{class:W(["online-status",{online:B()}])},null,2)]),l("div",Ce,[l("h4",null,a(n.nickname||n.username),1),l("p",Fe,"@"+a(n.username),1),l("div",xe,[l("span",Ve,[t(u,null,{default:s(()=>[t(i(se))]),_:1}),o(" 第"+a(n.maxLevel)+"关 ",1)]),l("span",be,[t(u,null,{default:s(()=>[t(i(ne))]),_:1}),o(" "+a(n.totalStars)+"⭐ ",1)]),l("span",he,[t(u,null,{default:s(()=>[t(i(ae))]),_:1}),o(" "+a(n.totalScore)+"分 ",1)])])]),l("div",De,[l("div",Ue,[e[15]||(e[15]=l("span",{class:"rank-label"},"排名",-1)),l("span",ze,"#"+a(n.ranking||"--"),1)]),l("div",Se,[t(r,{size:"small",onClick:g=>L(n)},{default:s(()=>[t(u,null,{default:s(()=>[t(i(oe))]),_:1}),e[16]||(e[16]=o(" 挑战 ",-1))]),_:2,__:[16]},1032,["onClick"]),t(O,{onCommand:M},{dropdown:s(()=>[t(T,null,{default:s(()=>[t(R,{command:`remove-${n.userId}`},{default:s(()=>e[17]||(e[17]=[o(" 删除好友 ",-1)])),_:2,__:[17]},1032,["command"])]),_:2},1024)]),default:s(()=>[t(r,{size:"small",circle:""},{default:s(()=>[t(u,null,{default:s(()=>[t(i(de))]),_:1})]),_:1})]),_:2},1024)])])]))),128))])]))]),t(z,{modelValue:p.value,"onUpdate:modelValue":e[5]||(e[5]=n=>p.value=n),title:"添加好友",width:"400px"},{footer:s(()=>[t(r,{onClick:e[4]||(e[4]=n=>p.value=!1)},{default:s(()=>e[19]||(e[19]=[o("取消",-1)])),_:1,__:[19]}),t(r,{type:"primary",onClick:E,loading:x.value},{default:s(()=>e[20]||(e[20]=[o(" 添加好友 ",-1)])),_:1,__:[20]},8,["loading"])]),default:s(()=>[l("div",$e,[t(K,{model:I,rules:A,ref_key:"addFriendFormRef",ref:V},{default:s(()=>[t(q,{label:"用户ID",prop:"friendId"},{default:s(()=>[t(P,{modelValue:I.friendId,"onUpdate:modelValue":e[3]||(e[3]=n=>I.friendId=n),placeholder:"请输入好友的用户ID",type:"number"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),e[18]||(e[18]=l("div",{class:"add-friend-tips"},[l("h4",null,"如何找到用户ID？"),l("ul",null,[l("li",null,"在个人资料页面可以查看自己的用户ID"),l("li",null,"朋友可以在排行榜中找到并分享用户ID"),l("li",null,"用户ID是注册时系统自动分配的唯一数字")])],-1))])]),_:1},8,["modelValue"]),t(z,{modelValue:k.value,"onUpdate:modelValue":e[7]||(e[7]=n=>k.value=n),title:"挑战好友",width:"400px"},{footer:s(()=>[t(r,{onClick:e[6]||(e[6]=n=>k.value=!1)},{default:s(()=>e[24]||(e[24]=[o("取消",-1)])),_:1,__:[24]}),t(r,{type:"primary",onClick:N},{default:s(()=>e[25]||(e[25]=[o(" 开始挑战 ",-1)])),_:1,__:[25]})]),default:s(()=>{var n,g;return[_.value?(v(),c("div",Ae,[l("div",Be,[t(U,{size:60,src:_.value.avatarUrl},{default:s(()=>{var S;return[o(a(((S=_.value.nickname)==null?void 0:S.charAt(0))||"U"),1)]}),_:1},8,["src"]),l("h3",null,"挑战 "+a(_.value.nickname||_.value.username),1)]),l("div",Le,[e[23]||(e[23]=l("p",null,"向好友发起挑战，比比谁的分数更高！",-1)),l("div",Me,[l("div",Ee,[e[21]||(e[21]=l("h4",null,"我",-1)),l("p",null,"最高分: "+a(((n=i(h).currentUser)==null?void 0:n.totalScore)||0),1),l("p",null,"关卡: "+a(((g=i(h).currentUser)==null?void 0:g.maxLevel)||1),1)]),e[22]||(e[22]=l("div",{class:"vs-divider"},"VS",-1)),l("div",Ne,[l("h4",null,a(_.value.nickname||_.value.username),1),l("p",null,"最高分: "+a(_.value.totalScore),1),l("p",null,"关卡: "+a(_.value.maxLevel),1)])])])])):Y("",!0)]}),_:1},8,["modelValue"])])}}}),Qe=ue(Re,[["__scopeId","data-v-35b5ef3e"]]);export{Qe as default};
