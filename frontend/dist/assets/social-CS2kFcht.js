import{b as r}from"./index-zrXExFMB.js";const t={addFriend:e=>r.post(`/friend/add/${e}`),removeFriend:e=>r.delete(`/friend/remove/${e}`),getFriends:()=>r.get("/friend/list"),checkFriendship:e=>r.get(`/friend/check/${e}`),getFriendCount:()=>r.get("/friend/count")},a={getGlobalLeaderboard:(e=50)=>r.get("/leaderboard/global",{limit:e}),getFriendsLeaderboard:(e=20)=>r.get("/leaderboard/friends",{limit:e}),getLevelLeaderboard:(e,d=20)=>r.get(`/leaderboard/level/${e}`,{limit:d}),getMyRanking:()=>r.get("/leaderboard/my-ranking"),getMyFriendRanking:()=>r.get("/leaderboard/my-friend-ranking")};export{t as f,a as l};
