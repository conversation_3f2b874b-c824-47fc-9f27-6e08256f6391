<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>合十消 - 数字消除游戏</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      #app {
        min-height: 100vh;
      }

      /* Loading 样式 */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .loading-content {
        text-align: center;
        color: white;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-zrXExFMB.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-DYfMlv50.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-BdXZoB_7.js">
    <link rel="stylesheet" crossorigin href="/assets/index-BT2iZntA.css">
  </head>
  <body>
    <div id="app">
      <div class="loading">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h2>合十消</h2>
          <p>游戏加载中...</p>
        </div>
      </div>
    </div>
  </body>
</html>