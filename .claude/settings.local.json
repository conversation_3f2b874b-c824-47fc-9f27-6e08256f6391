{"permissions": {"allow": ["Bash(mvn spring-boot:run)", "Bash(ls:*)", "Bash(java:*)", "Bash(brew install:*)", "Bash(npm:*)", "Bash(grep:*)", "<PERSON><PERSON>(mvn:*)", "Bash(brew list:*)", "Bash(brew services start:*)", "<PERSON><PERSON>(mysql:*)", "Bash(export:*)", "Bash(export PATH=\"$JAVA_HOME/bin:$PATH\")", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(rm:*)", "<PERSON><PERSON>(true)", "Bash(npx @dcloudio/uvm@latest create uniapp-frontend)", "Bash(vue create:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(touch:*)", "Bash(timeout 30 npm run dev:mp-weixin:*)"], "deny": []}}