[INFO] Scanning for projects...
[INFO] 
[INFO] ---------------------< com.heshixiao:game-backend >---------------------
[INFO] Building heshixiao-game-backend 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:2.7.14:run (default-cli) > test-compile @ game-backend >>>
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ game-backend ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 1 resource
[INFO] Copying 0 resource
[INFO] 
[INFO] --- compiler:3.10.1:compile (default-compile) @ game-backend ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.2.0:testResources (default-testResources) @ game-backend ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory /Users/<USER>/dev/wxapp/backend/src/test/resources
[INFO] 
[INFO] --- compiler:3.10.1:testCompile (default-testCompile) @ game-backend ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:2.7.14:run (default-cli) < test-compile @ game-backend <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:2.7.14:run (default-cli) @ game-backend ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m             [2m (v2.7.14)[0;39m

2025-07-27 12:35:25 [main] INFO  c.h.game.GameBackendApplication - Starting GameBackendApplication using Java 23.0.2 on qingjundeMacBook-Air.local with PID 42267 (/Users/<USER>/dev/wxapp/backend/target/classes started by pxw in /Users/<USER>/dev/wxapp/backend)
2025-07-27 12:35:25 [main] DEBUG c.h.game.GameBackendApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-27 12:35:25 [main] INFO  c.h.game.GameBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 12:35:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-27 12:35:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 12:35:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-27 12:35:26 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 12:35:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 328 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
2025-07-27 12:35:26 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-27 12:35:26 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
Property 'mapperLocations' was not specified.
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        ******* 
2025-07-27 12:35:26 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-07-27 12:35:26 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-27 12:35:26 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-27 12:35:26 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 12:35:26 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 12:35:26 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  1.662 s
[INFO] Finished at: 2025-07-27T12:35:26+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:2.7.14:run (default-cli) on project game-backend: Application finished with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
