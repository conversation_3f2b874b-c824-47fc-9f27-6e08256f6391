package com.heshixiao.game.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class EndGameDTO {
    @NotBlank(message = "游戏会话ID不能为空")
    private String sessionId;
    
    @NotNull(message = "分数不能为空")
    private Integer score;
    
    @NotNull(message = "使用步数不能为空")
    private Integer movesUsed;
    
    private Integer timeUsed;
    
    private String itemsUsed;
    
    @NotNull(message = "游戏结果不能为空")
    private Boolean isVictory;
    
    private String endReason;
}