package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("game_sessions")
public class GameSession {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String sessionId;
    
    private Long userId;
    
    private Integer levelId;
    
    private Integer score;
    
    private Integer movesUsed;
    
    private Integer timeUsed;
    
    private String itemsUsed;
    
    private Boolean isVictory;
    
    private String endReason;
    
    private Integer starsEarned;
    
    private LocalDateTime createdAt;
}