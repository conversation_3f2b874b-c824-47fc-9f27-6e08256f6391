package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("user_progress")
public class UserProgress {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private Integer levelId;
    
    private Integer stars = 0;
    
    private Integer bestScore = 0;
    
    private Integer playCount = 0;
    
    private Boolean isCompleted = false;
    
    private LocalDateTime completedAt;
    
    private LocalDateTime lastPlayedAt;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}