package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("user_items")
public class UserItem {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private String itemType; // "bomb", "shuffle", "hint" etc.
    
    private Integer quantity = 0;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}