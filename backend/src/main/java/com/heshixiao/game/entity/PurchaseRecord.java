package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("purchase_records")
public class PurchaseRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private Integer shopItemId;
    
    private String orderNo;  // 添加订单号字段
    
    private Integer quantity;
    
    private BigDecimal unitPrice;  // 修改为BigDecimal类型，匹配数据库
    
    private BigDecimal totalPrice;  // 修改为BigDecimal类型，匹配数据库
    
    private String paymentMethod;
    
    private String transactionId;
    
    private String status;
    
    private LocalDateTime payTime;  // 添加支付时间字段
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;  // 添加更新时间字段
}