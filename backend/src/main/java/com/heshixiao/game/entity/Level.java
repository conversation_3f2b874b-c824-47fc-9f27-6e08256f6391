package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("levels")
public class Level {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer levelNumber;

    private String levelName;

    private String targetType;

    private Integer targetValue;

    private String targetDescription;

    private Integer maxMoves;

    private Integer timeLimit;

    private Integer boardWidth;

    private Integer boardHeight;

    /**
     * 棋盘配置(障碍物位置等)，以JSON字符串形式存储。
     * 在Service层可以将其转换为具体的对象或Map。
     */
    private String boardConfig;

    /**
     * 三星评分标准 [1星, 2星, 3星]，以JSON字符串形式存储。
     * 例如: "[1000, 5000, 10000]"
     */
    private String starScores;

    private Integer rewardCoins;

    private String unlockCondition;

    private Integer difficulty;

    private Boolean isActive;

    private LocalDateTime createdAt;
}