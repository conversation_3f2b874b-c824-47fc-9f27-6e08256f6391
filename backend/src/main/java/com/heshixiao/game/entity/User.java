package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("users")
public class User {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String username;
    
    private String password;
    
    private String openid;
    
    private String unionid;
    
    private String nickname;
    
    private String avatarUrl;
    
    private String phone;
    
    private Integer coins = 0;
    
    private Integer diamonds = 0;
    
    private Integer energy = 5;
    
    private Integer maxEnergy = 5;
    
    private LocalDateTime lastEnergyTime;
    
    private Long totalScore = 0L;
    
    private Integer maxLevel = 1;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}