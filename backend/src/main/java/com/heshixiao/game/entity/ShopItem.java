package com.heshixiao.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("shop_items")
public class ShopItem {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private String itemName;
    
    private String itemType;
    
    private Integer itemValue;
    
    private String priceType; // "coins" or "diamonds"
    
    private BigDecimal price;
    
    private Boolean isActive = true;
    
    private Integer sortOrder = 0;
    
    private String description; // 添加商品描述
    
    private String iconUrl; // 添加商品图标
    
    private LocalDateTime createdAt;
}