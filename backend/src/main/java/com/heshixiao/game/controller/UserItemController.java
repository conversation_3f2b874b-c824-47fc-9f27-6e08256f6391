package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.dto.UseItemDTO;
import com.heshixiao.game.service.UserItemService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.UserItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/items")
@CrossOrigin
public class UserItemController {

    @Autowired
    private UserItemService userItemService;

    @Autowired
    private JwtUtil jwtUtil;

    @GetMapping("/inventory")
    public Result<List<UserItemVO>> getUserItems(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<UserItemVO> items = userItemService.getUserItems(userId);
            return Result.success(items);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/quantity/{itemType}")
    public Result<Integer> getItemQuantity(@PathVariable String itemType,
                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer quantity = userItemService.getUserItemQuantity(userId, itemType);
            return Result.success(quantity);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/use")
    public Result<Boolean> useItem(@Valid @RequestBody UseItemDTO useItemDTO,
                                  HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean success = userItemService.useUserItem(userId, useItemDTO);
            if (success) {
                return Result.success("道具使用成功", true);
            } else {
                return Result.error("道具不足或使用失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/check/{itemType}/{quantity}")
    public Result<Boolean> checkItemAvailability(@PathVariable String itemType,
                                                @PathVariable Integer quantity,
                                                HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean hasEnough = userItemService.hasEnoughItems(userId, itemType, quantity);
            return Result.success(hasEnough);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/quantities")
    public Result<Map<String, Integer>> getAllItemQuantities(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Map<String, Integer> quantities = userItemService.getAllUserItemQuantities(userId);
            return Result.success(quantities);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}