package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.entity.ShopItem;
import com.heshixiao.game.service.ShopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/shop")
@CrossOrigin
public class ShopController {

    @Autowired
    private ShopService shopService;

    @GetMapping("/items")
    public Result<List<ShopItem>> getAllItems() {
        try {
            List<ShopItem> items = shopService.getAllActiveItems();
            return Result.success(items);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/items/type/{itemType}")
    public Result<List<ShopItem>> getItemsByType(@PathVariable String itemType) {
        try {
            List<ShopItem> items = shopService.getItemsByType(itemType);
            return Result.success(items);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/items/{itemId}")
    public Result<ShopItem> getItemById(@PathVariable Integer itemId) {
        try {
            ShopItem item = shopService.getItemById(itemId);
            if (item == null) {
                return Result.error("商品不存在或已下架");
            }
            return Result.success(item);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}