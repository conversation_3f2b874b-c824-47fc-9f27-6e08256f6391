package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.dto.UserLoginDTO;
import com.heshixiao.game.dto.UserRegisterDTO;
import com.heshixiao.game.dto.WxLoginDTO;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.service.UserService;
import com.heshixiao.game.service.WxService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.LoginResponseVO;
import com.heshixiao.game.vo.UserInfoVO;
import com.heshixiao.game.vo.UserStatsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/user")
@CrossOrigin
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private WxService wxService;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/register")
    public Result<LoginResponseVO> register(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        try {
            LoginResponseVO response = userService.register(userRegisterDTO);
            return Result.success("注册成功", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/login")
    public Result<LoginResponseVO> login(@Valid @RequestBody UserLoginDTO userLoginDTO) {
        try {
            LoginResponseVO response = userService.login(userLoginDTO);
            return Result.success("登录成功", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/wx-login")
    public Result<LoginResponseVO> wxLogin(@Valid @RequestBody WxLoginDTO wxLoginDTO) {
        try {
            LoginResponseVO response = wxService.wxLogin(wxLoginDTO);
            return Result.success("微信登录成功", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/info")
    public Result<UserInfoVO> getUserInfo(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            UserInfoVO userInfo = userService.getUserInfo(userId);
            return Result.success(userInfo);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.checkUsernameExists(username);
            return Result.success(!exists);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/recover-energy")
    public Result<Boolean> recoverEnergy(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean success = userService.recoverEnergy(userId);
            return Result.success(success);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/stats")
    public Result<UserStatsVO> getUserStats(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            User user = userService.getUserById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            UserStatsVO stats = new UserStatsVO();
            stats.setCoins(user.getCoins());
            stats.setDiamonds(user.getDiamonds());
            stats.setEnergy(user.getEnergy());
            stats.setMaxEnergy(user.getMaxEnergy());
            stats.setTotalScore(user.getTotalScore());
            
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}