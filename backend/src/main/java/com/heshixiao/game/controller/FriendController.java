package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.service.FriendService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.FriendInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/friend")
@CrossOrigin
public class FriendController {

    @Autowired
    private FriendService friendService;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/add/{friendId}")
    public Result<Boolean> addFriend(@PathVariable Long friendId,
                                    HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean success = friendService.addFriend(userId, friendId);
            return Result.success("添加好友成功", success);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/remove/{friendId}")
    public Result<Boolean> removeFriend(@PathVariable Long friendId,
                                       HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean success = friendService.removeFriend(userId, friendId);
            return Result.success("删除好友成功", success);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/list")
    public Result<List<FriendInfoVO>> getFriends(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<FriendInfoVO> friends = friendService.getUserFriends(userId);
            return Result.success(friends);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/check/{friendId}")
    public Result<Boolean> checkFriendship(@PathVariable Long friendId,
                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean isFriend = friendService.isFriend(userId, friendId);
            return Result.success(isFriend);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/count")
    public Result<Integer> getFriendCount(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer count = friendService.getFriendCount(userId);
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}