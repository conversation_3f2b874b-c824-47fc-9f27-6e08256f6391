package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.dto.PurchaseDTO;
import com.heshixiao.game.entity.PurchaseRecord;
import com.heshixiao.game.service.PurchaseService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.PurchaseResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/purchase")
@CrossOrigin
public class PurchaseController {

    @Autowired
    private PurchaseService purchaseService;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/buy")
    public Result<PurchaseResultVO> purchaseItem(@Valid @RequestBody PurchaseDTO purchaseDTO,
                                                HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            PurchaseResultVO result = purchaseService.purchaseItem(purchaseDTO, userId);
            return Result.success("购买成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/history")
    public Result<List<PurchaseRecord>> getPurchaseHistory(@RequestParam(defaultValue = "20") Integer limit,
                                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<PurchaseRecord> history = purchaseService.getUserPurchaseHistory(userId, limit);
            return Result.success(history);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/transaction/{transactionId}")
    public Result<PurchaseRecord> getPurchaseByTransactionId(@PathVariable String transactionId,
                                                            HttpServletRequest request) {
        try {
            getUserIdFromToken(request);
            PurchaseRecord record = purchaseService.getPurchaseByTransactionId(transactionId);
            if (record == null) {
                return Result.error("交易记录不存在");
            }
            return Result.success(record);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}