package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.entity.UserProgress;
import com.heshixiao.game.service.UserProgressService;
import com.heshixiao.game.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/progress")
@CrossOrigin
public class UserProgressController {

    @Autowired
    private UserProgressService userProgressService;

    @Autowired
    private JwtUtil jwtUtil;

    @GetMapping("/all")
    public Result<List<UserProgress>> getAllProgress(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<UserProgress> progress = userProgressService.getUserAllProgress(userId);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/level/{levelId}")
    public Result<UserProgress> getLevelProgress(@PathVariable Integer levelId,
                                                HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            UserProgress progress = userProgressService.getUserProgress(userId, levelId);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/max-level")
    public Result<Integer> getMaxLevel(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer maxLevel = userProgressService.getUserMaxLevel(userId);
            return Result.success(maxLevel != null ? maxLevel : 0);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/total-stars")
    public Result<Integer> getTotalStars(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer totalStars = userProgressService.getUserTotalStars(userId);
            return Result.success(totalStars != null ? totalStars : 0);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/completed-count")
    public Result<Integer> getCompletedLevelsCount(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer completedCount = userProgressService.getUserCompletedLevelsCount(userId);
            return Result.success(completedCount != null ? completedCount : 0);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/detailed")
    public Result<List<UserProgress>> getDetailedProgress(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<UserProgress> progress = userProgressService.getUserDetailedProgress(userId);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}