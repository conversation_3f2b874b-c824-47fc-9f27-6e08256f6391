package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.dto.EndGameDTO;
import com.heshixiao.game.dto.StartGameDTO;
import com.heshixiao.game.entity.GameSession;
import com.heshixiao.game.service.GameService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.GameSessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/game")
@CrossOrigin
public class GameController {

    @Autowired
    private GameService gameService;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/start")
    public Result<GameSessionVO> startGame(@Valid @RequestBody StartGameDTO startGameDTO, 
                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            GameSessionVO response = gameService.startGame(startGameDTO, userId);
            return Result.success("游戏开始", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/end")
    public Result<GameSessionVO> endGame(@Valid @RequestBody EndGameDTO endGameDTO,
                                        HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            GameSessionVO response = gameService.endGame(endGameDTO, userId);
            return Result.success("游戏结束", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/session/{sessionId}")
    public Result<GameSession> getGameSession(@PathVariable String sessionId,
                                             HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            if (!gameService.validateGameSession(sessionId, userId)) {
                return Result.error(403, "无权限访问此游戏会话");
            }
            
            GameSession gameSession = gameService.getGameSession(sessionId);
            return Result.success(gameSession);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/history")
    public Result<List<GameSession>> getGameHistory(@RequestParam(defaultValue = "10") Integer limit,
                                                   HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<GameSession> history = gameService.getUserGameHistory(userId, limit);
            return Result.success(history);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}