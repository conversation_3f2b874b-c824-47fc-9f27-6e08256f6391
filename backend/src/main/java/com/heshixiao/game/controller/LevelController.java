package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.service.LevelService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.LevelInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/level")
@CrossOrigin
public class LevelController {

    @Autowired
    private LevelService levelService;

    @Autowired
    private JwtUtil jwtUtil;

    @GetMapping("/list")
    public Result<List<LevelInfoVO>> getAllLevels(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<LevelInfoVO> levels = levelService.getAllLevels(userId);
            return Result.success(levels);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/{levelId}")
    public Result<LevelInfoVO> getLevelInfo(@PathVariable Integer levelId,
                                           HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            LevelInfoVO levelInfo = levelService.getLevelInfo(levelId, userId);
            return Result.success(levelInfo);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/difficulty/{difficulty}")
    public Result<List<LevelInfoVO>> getLevelsByDifficulty(@PathVariable Integer difficulty,
                                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<LevelInfoVO> levels = levelService.getLevelsByDifficulty(difficulty, userId);
            return Result.success(levels);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/check-unlock/{levelId}")
    public Result<Boolean> checkLevelUnlocked(@PathVariable Integer levelId,
                                             HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            boolean isUnlocked = levelService.isLevelUnlocked(levelId, userId);
            return Result.success(isUnlocked);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}