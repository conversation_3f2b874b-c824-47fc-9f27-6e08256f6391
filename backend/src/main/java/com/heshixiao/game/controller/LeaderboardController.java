package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import com.heshixiao.game.service.LeaderboardService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.LeaderboardVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/leaderboard")
@CrossOrigin
public class LeaderboardController {

    @Autowired
    private LeaderboardService leaderboardService;

    @Autowired
    private JwtUtil jwtUtil;

    @GetMapping("/global")
    public Result<List<LeaderboardVO>> getGlobalLeaderboard(@RequestParam(defaultValue = "50") Integer limit,
                                                           HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<LeaderboardVO> leaderboard = leaderboardService.getGlobalLeaderboard(limit, userId);
            return Result.success(leaderboard);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/friends")
    public Result<List<LeaderboardVO>> getFriendsLeaderboard(@RequestParam(defaultValue = "20") Integer limit,
                                                            HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<LeaderboardVO> leaderboard = leaderboardService.getFriendsLeaderboard(userId, limit);
            return Result.success(leaderboard);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/level/{levelId}")
    public Result<List<LeaderboardVO>> getLevelLeaderboard(@PathVariable Integer levelId,
                                                          @RequestParam(defaultValue = "20") Integer limit,
                                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            List<LeaderboardVO> leaderboard = leaderboardService.getLevelLeaderboard(levelId, limit, userId);
            return Result.success(leaderboard);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/my-ranking")
    public Result<Integer> getMyRanking(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer ranking = leaderboardService.getUserRanking(userId);
            return Result.success(ranking);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/my-friend-ranking")
    public Result<Integer> getMyFriendRanking(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            Integer ranking = leaderboardService.getUserFriendRanking(userId);
            return Result.success(ranking);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new RuntimeException("请先登录");
        }
        
        token = token.substring(7);
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("登录已过期，请重新登录");
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }
}