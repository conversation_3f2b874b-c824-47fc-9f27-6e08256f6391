package com.heshixiao.game.service.impl;

import com.heshixiao.game.entity.UserProgress;
import com.heshixiao.game.mapper.UserProgressMapper;
import com.heshixiao.game.service.UserProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class UserProgressServiceImpl implements UserProgressService {

    @Autowired
    private UserProgressMapper userProgressMapper;

    @Override
    public UserProgress getUserProgress(Long userId, Integer levelId) {
        return userProgressMapper.findByUserIdAndLevelId(userId, levelId);
    }

    @Override
    public List<UserProgress> getUserAllProgress(Long userId) {
        return userProgressMapper.findByUserId(userId);
    }

    @Override
    public boolean updateProgress(Long userId, Integer levelId, Integer score, Integer stars, Boolean isCompleted) {
        try {
            UserProgress existingProgress = userProgressMapper.findByUserIdAndLevelId(userId, levelId);
            
            if (existingProgress == null) {
                // 尝试插入新记录
                UserProgress newProgress = new UserProgress();
                newProgress.setUserId(userId);
                newProgress.setLevelId(levelId);
                newProgress.setBestScore(score);
                newProgress.setStars(stars);
                newProgress.setPlayCount(1);
                newProgress.setIsCompleted(isCompleted);
                if (isCompleted) {
                    newProgress.setCompletedAt(LocalDateTime.now());
                }
                newProgress.setLastPlayedAt(LocalDateTime.now());
                
                try {
                    return userProgressMapper.insert(newProgress) > 0;
                } catch (Exception e) {
                    // 如果插入失败（可能是唯一约束冲突），重新查询并更新
                    if (e.getMessage().contains("Duplicate entry")) {
                        existingProgress = userProgressMapper.findByUserIdAndLevelId(userId, levelId);
                        if (existingProgress != null) {
                            return updateExistingProgress(existingProgress, score, stars, isCompleted);
                        }
                    }
                    throw e;
                }
            } else {
                return updateExistingProgress(existingProgress, score, stars, isCompleted);
            }
        } catch (Exception e) {
            throw new RuntimeException("更新用户进度失败", e);
        }
    }
    
    private boolean updateExistingProgress(UserProgress existingProgress, Integer score, Integer stars, Boolean isCompleted) {
        existingProgress.setPlayCount(existingProgress.getPlayCount() + 1);
        existingProgress.setLastPlayedAt(LocalDateTime.now());
        
        if (score > existingProgress.getBestScore()) {
            existingProgress.setBestScore(score);
        }
        
        if (stars > existingProgress.getStars()) {
            existingProgress.setStars(stars);
        }
        
        if (isCompleted && !existingProgress.getIsCompleted()) {
            existingProgress.setIsCompleted(true);
            existingProgress.setCompletedAt(LocalDateTime.now());
        }
        
        return userProgressMapper.updateById(existingProgress) > 0;
    }

    @Override
    public Integer getUserMaxLevel(Long userId) {
        return userProgressMapper.findMaxCompletedLevelByUserId(userId);
    }

    @Override
    public Integer getUserTotalStars(Long userId) {
        return userProgressMapper.countStarsByUserId(userId, 1);
    }

    @Override
    public boolean createOrUpdateProgress(UserProgress userProgress) {
        UserProgress existingProgress = userProgressMapper.findByUserIdAndLevelId(
            userProgress.getUserId(), userProgress.getLevelId());
        
        if (existingProgress == null) {
            return userProgressMapper.insert(userProgress) > 0;
        } else {
            userProgress.setId(existingProgress.getId());
            return userProgressMapper.updateById(userProgress) > 0;
        }
    }

    @Override
    public Integer getUserCompletedLevelsCount(Long userId) {
        return userProgressMapper.countCompletedLevels(userId);
    }

    @Override
    public List<UserProgress> getUserDetailedProgress(Long userId) {
        return userProgressMapper.findDetailedProgressByUserId(userId);
    }
}