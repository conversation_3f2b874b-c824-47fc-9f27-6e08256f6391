package com.heshixiao.game.service.impl;

import com.heshixiao.game.dto.UseItemDTO;
import com.heshixiao.game.entity.UserItem;
import com.heshixiao.game.mapper.UserItemMapper;
import com.heshixiao.game.service.UserItemService;
import com.heshixiao.game.vo.UserItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UserItemServiceImpl implements UserItemService {

    @Autowired
    private UserItemMapper userItemMapper;

    private static final Map<String, String> ITEM_NAMES = new HashMap<>();
    private static final Map<String, String> ITEM_DESCRIPTIONS = new HashMap<>();

    static {
        ITEM_NAMES.put("bomb", "炸弹道具");
        ITEM_NAMES.put("shuffle", "重排道具");
        ITEM_NAMES.put("hint", "提示道具");

        ITEM_DESCRIPTIONS.put("bomb", "消除3x3区域内的所有方块");
        ITEM_DESCRIPTIONS.put("shuffle", "重新随机排列棋盘上的数字");
        ITEM_DESCRIPTIONS.put("hint", "高亮显示可消除的数字组合");
    }

    @Override
    public List<UserItemVO> getUserItems(Long userId) {
        List<UserItem> userItems = userItemMapper.findByUserId(userId);
        List<UserItemVO> result = new ArrayList<>();

        for (String itemType : ITEM_NAMES.keySet()) {
            UserItemVO vo = new UserItemVO();
            vo.setItemType(itemType);
            vo.setItemName(ITEM_NAMES.get(itemType));
            vo.setDescription(ITEM_DESCRIPTIONS.get(itemType));

            UserItem userItem = userItems.stream()
                .filter(item -> item.getItemType().equals(itemType))
                .findFirst()
                .orElse(null);

            vo.setQuantity(userItem != null ? userItem.getQuantity() : 0);
            result.add(vo);
        }

        return result;
    }

    @Override
    public UserItem getUserItem(Long userId, String itemType) {
        return userItemMapper.findByUserIdAndItemType(userId, itemType);
    }

    @Override
    public boolean addUserItem(Long userId, String itemType, Integer quantity) {
        UserItem existingItem = userItemMapper.findByUserIdAndItemType(userId, itemType);

        if (existingItem == null) {
            UserItem newItem = new UserItem();
            newItem.setUserId(userId);
            newItem.setItemType(itemType);
            newItem.setQuantity(quantity);
            newItem.setCreatedAt(LocalDateTime.now());
            newItem.setUpdatedAt(LocalDateTime.now());
            return userItemMapper.insert(newItem) > 0;
        } else {
            existingItem.setQuantity(existingItem.getQuantity() + quantity);
            existingItem.setUpdatedAt(LocalDateTime.now());
            return userItemMapper.updateById(existingItem) > 0;
        }
    }

    @Override
    public boolean useUserItem(Long userId, UseItemDTO useItemDTO) {
        UserItem userItem = userItemMapper.findByUserIdAndItemType(userId, useItemDTO.getItemType());
        
        if (userItem == null || userItem.getQuantity() < useItemDTO.getQuantity()) {
            return false;
        }

        userItem.setQuantity(userItem.getQuantity() - useItemDTO.getQuantity());
        userItem.setUpdatedAt(LocalDateTime.now());
        return userItemMapper.updateById(userItem) > 0;
    }

    @Override
    public Integer getUserItemQuantity(Long userId, String itemType) {
        UserItem userItem = userItemMapper.findByUserIdAndItemType(userId, itemType);
        return userItem != null ? userItem.getQuantity() : 0;
    }

    @Override
    public boolean hasEnoughItems(Long userId, String itemType, Integer requiredQuantity) {
        Integer currentQuantity = getUserItemQuantity(userId, itemType);
        return currentQuantity >= requiredQuantity;
    }

    @Override
    public Map<String, Integer> getAllUserItemQuantities(Long userId) {
        List<UserItem> userItems = userItemMapper.findByUserId(userId);
        Map<String, Integer> quantities = new HashMap<>();
        
        // 初始化所有道具类型为0
        for (String itemType : ITEM_NAMES.keySet()) {
            quantities.put(itemType, 0);
        }
        
        // 更新实际拥有的道具数量
        for (UserItem userItem : userItems) {
            if (ITEM_NAMES.containsKey(userItem.getItemType())) {
                quantities.put(userItem.getItemType(), userItem.getQuantity());
            }
        }
        
        return quantities;
    }
}