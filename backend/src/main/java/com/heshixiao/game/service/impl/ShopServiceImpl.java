package com.heshixiao.game.service.impl;

import com.heshixiao.game.entity.ShopItem;
import com.heshixiao.game.mapper.ShopItemMapper;
import com.heshixiao.game.service.ShopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ShopServiceImpl implements ShopService {

    @Autowired
    private ShopItemMapper shopItemMapper;

    @Override
    public List<ShopItem> getAllActiveItems() {
        return shopItemMapper.findActiveItems();
    }

    @Override
    public List<ShopItem> getItemsByType(String itemType) {
        return shopItemMapper.findActiveItemsByType(itemType);
    }

    @Override
    public ShopItem getItemById(Integer itemId) {
        ShopItem item = shopItemMapper.selectById(itemId);
        return (item != null && item.getIsActive()) ? item : null;
    }
}