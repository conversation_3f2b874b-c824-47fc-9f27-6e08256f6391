package com.heshixiao.game.service;

import com.heshixiao.game.dto.EndGameDTO;
import com.heshixiao.game.dto.StartGameDTO;
import com.heshixiao.game.entity.GameSession;
import com.heshixiao.game.vo.GameSessionVO;

import java.util.List;

public interface GameService {
    
    GameSessionVO startGame(StartGameDTO startGameDTO, Long userId);
    
    GameSessionVO endGame(EndGameDTO endGameDTO, Long userId);
    
    GameSession getGameSession(String sessionId);
    
    List<GameSession> getUserGameHistory(Long userId, Integer limit);
    
    boolean validateGameSession(String sessionId, Long userId);
}