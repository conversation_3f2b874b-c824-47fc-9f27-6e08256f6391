package com.heshixiao.game.service;

import com.heshixiao.game.vo.FriendInfoVO;

import java.util.List;

public interface FriendService {
    
    boolean addFriend(Long userId, Long friendId);
    
    boolean removeFriend(Long userId, Long friendId);
    
    List<FriendInfoVO> getUserFriends(Long userId);
    
    boolean isFriend(Long userId, Long friendId);
    
    Integer getFriendCount(Long userId);
    
    List<Long> getFriendIds(Long userId);
}