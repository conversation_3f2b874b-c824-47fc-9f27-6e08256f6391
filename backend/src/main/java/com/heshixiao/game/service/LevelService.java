package com.heshixiao.game.service;

import com.heshixiao.game.entity.Level;
import com.heshixiao.game.vo.LevelInfoVO;

import java.util.List;

public interface LevelService {
    
    List<LevelInfoVO> getAllLevels(Long userId);
    
    LevelInfoVO getLevelInfo(Integer levelId, Long userId);
    
    Level getLevelById(Integer levelId);
    
    boolean isLevelUnlocked(Integer levelId, Long userId);
    
    List<LevelInfoVO> getLevelsByDifficulty(Integer difficulty, Long userId);
}