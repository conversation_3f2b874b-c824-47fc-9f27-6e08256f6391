package com.heshixiao.game.service;

import com.heshixiao.game.dto.WxLoginDTO;
import com.heshixiao.game.vo.LoginResponseVO;

public interface WxService {
    
    /**
     * 微信登录
     * @param wxLoginDTO 微信登录参数
     * @return 登录响应
     */
    LoginResponseVO wxLogin(WxLoginDTO wxLoginDTO);
    
    /**
     * 获取微信 Access Token
     * @param code 微信登录码
     * @return 微信会话信息
     */
    WxSessionInfo getWxSession(String code);
    
    /**
     * 微信会话信息
     */
    class WxSessionInfo {
        private String openid;
        private String sessionKey;
        private String unionid;
        
        // getters and setters
        public String getOpenid() { return openid; }
        public void setOpenid(String openid) { this.openid = openid; }
        
        public String getSessionKey() { return sessionKey; }
        public void setSessionKey(String sessionKey) { this.sessionKey = sessionKey; }
        
        public String getUnionid() { return unionid; }
        public void setUnionid(String unionid) { this.unionid = unionid; }
    }
}