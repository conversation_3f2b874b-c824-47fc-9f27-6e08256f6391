package com.heshixiao.game.service.impl;

import com.heshixiao.game.entity.Level;
import com.heshixiao.game.entity.UserProgress;
import com.heshixiao.game.mapper.LevelMapper;
import com.heshixiao.game.service.LevelService;
import com.heshixiao.game.service.UserProgressService;
import com.heshixiao.game.vo.LevelInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LevelServiceImpl implements LevelService {

    @Autowired
    private LevelMapper levelMapper;

    @Autowired
    private UserProgressService userProgressService;

    @Override
    public List<LevelInfoVO> getAllLevels(Long userId) {
        List<Level> levels = levelMapper.findAllOrderByLevelNumber();
        List<LevelInfoVO> levelInfos = new ArrayList<>();
        
        for (Level level : levels) {
            LevelInfoVO levelInfo = convertToLevelInfoVO(level, userId);
            levelInfos.add(levelInfo);
        }
        
        return levelInfos;
    }

    @Override
    public LevelInfoVO getLevelInfo(Integer levelId, Long userId) {
        Level level = levelMapper.selectById(levelId);
        if (level == null) {
            throw new RuntimeException("关卡不存在");
        }
        
        return convertToLevelInfoVO(level, userId);
    }

    @Override
    public Level getLevelById(Integer levelId) {
        return levelMapper.selectById(levelId);
    }

    @Override
    public boolean isLevelUnlocked(Integer levelId, Long userId) {
        Level level = levelMapper.selectById(levelId);
        if (level == null) {
            return false;
        }
        
        if (level.getLevelNumber() == 1) {
            return true;
        }
        
        Integer maxCompletedLevel = userProgressService.getUserMaxLevel(userId);
        return maxCompletedLevel != null && level.getLevelNumber() <= maxCompletedLevel + 1;
    }

    @Override
    public List<LevelInfoVO> getLevelsByDifficulty(Integer difficulty, Long userId) {
        List<Level> levels = levelMapper.findByDifficulty(difficulty);
        List<LevelInfoVO> levelInfos = new ArrayList<>();
        
        for (Level level : levels) {
            LevelInfoVO levelInfo = convertToLevelInfoVO(level, userId);
            levelInfos.add(levelInfo);
        }
        
        return levelInfos;
    }

    private LevelInfoVO convertToLevelInfoVO(Level level, Long userId) {
        LevelInfoVO levelInfo = new LevelInfoVO();
        BeanUtils.copyProperties(level, levelInfo);
        
        levelInfo.setIsUnlocked(isLevelUnlocked(level.getId(), userId));
        
        if (userId != null) {
            UserProgress progress = userProgressService.getUserProgress(userId, level.getId());
            if (progress != null) {
                levelInfo.setUserStars(progress.getStars());
                levelInfo.setUserBestScore(progress.getBestScore());
            } else {
                levelInfo.setUserStars(0);
                levelInfo.setUserBestScore(0);
            }
        }
        
        return levelInfo;
    }
}