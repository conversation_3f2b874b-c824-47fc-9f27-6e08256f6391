package com.heshixiao.game.service.impl;

import com.heshixiao.game.dto.PurchaseDTO;
import com.heshixiao.game.entity.PurchaseRecord;
import com.heshixiao.game.entity.ShopItem;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.mapper.PurchaseRecordMapper;
import com.heshixiao.game.service.PurchaseService;
import com.heshixiao.game.service.ShopService;
import com.heshixiao.game.service.UserItemService;
import com.heshixiao.game.service.UserService;
import com.heshixiao.game.vo.PurchaseResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class PurchaseServiceImpl implements PurchaseService {

    @Autowired
    private PurchaseRecordMapper purchaseRecordMapper;

    @Autowired
    private ShopService shopService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserItemService userItemService;

    /**
     * 生成唯一的订单号
     * 格式: ORDER + yyyyMMddHHmmss + 6位随机数
     */
    private String generateOrderNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int randomNum = ThreadLocalRandom.current().nextInt(100000, 999999);
        return "ORDER" + timestamp + randomNum;
    }

    @Override
    @Transactional
    public PurchaseResultVO purchaseItem(PurchaseDTO purchaseDTO, Long userId) {
        ShopItem shopItem = shopService.getItemById(purchaseDTO.getShopItemId());
        if (shopItem == null) {
            throw new RuntimeException("商品不存在或已下架");
        }

        System.out.println("购买商品信息: " + shopItem.getItemName() + ", 类型: " + shopItem.getItemType() + ", 数值: " + shopItem.getItemValue());

        User user = userService.getUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        BigDecimal totalPrice = shopItem.getPrice().multiply(new BigDecimal(purchaseDTO.getQuantity()));

        if ("diamonds".equals(shopItem.getPriceType())) {
            if (user.getDiamonds() < totalPrice.intValue()) {
                throw new RuntimeException("钻石不足");
            }
        } else if ("coins".equals(shopItem.getPriceType())) {
            if (user.getCoins() < totalPrice.intValue()) {
                throw new RuntimeException("金币不足");
            }
        }

        // 生成订单号和交易ID
        String orderNo = generateOrderNo();
        String transactionId = UUID.randomUUID().toString();
        PurchaseRecord purchaseRecord = new PurchaseRecord();
        purchaseRecord.setUserId(userId);
        purchaseRecord.setShopItemId(purchaseDTO.getShopItemId());
        purchaseRecord.setOrderNo(orderNo);  // 设置订单号
        purchaseRecord.setQuantity(purchaseDTO.getQuantity());
        purchaseRecord.setUnitPrice(shopItem.getPrice());  // 设置单价
        purchaseRecord.setTotalPrice(totalPrice);
        purchaseRecord.setPaymentMethod(purchaseDTO.getPaymentMethod());
        purchaseRecord.setTransactionId(transactionId);
        purchaseRecord.setStatus("completed");
        purchaseRecord.setPayTime(LocalDateTime.now());  // 设置支付时间
        purchaseRecord.setCreatedAt(LocalDateTime.now());

        purchaseRecordMapper.insert(purchaseRecord);

        if ("diamonds".equals(shopItem.getPriceType())) {
            userService.updateUserDiamonds(userId, user.getDiamonds() - totalPrice.intValue());
        } else if ("coins".equals(shopItem.getPriceType())) {
            userService.updateUserCoins(userId, user.getCoins() - totalPrice.intValue());
        }

        PurchaseResultVO result = new PurchaseResultVO();
        result.setTransactionId(transactionId);
        result.setStatus("completed");

        switch (shopItem.getItemType()) {
            case "energy":
                userService.updateUserEnergy(userId, user.getEnergy() + shopItem.getItemValue() * purchaseDTO.getQuantity());
                break;
            case "coins":
                result.setRewardCoins(shopItem.getItemValue() * purchaseDTO.getQuantity());
                userService.updateUserCoins(userId, user.getCoins() + result.getRewardCoins());
                break;
            case "diamonds":
                result.setRewardDiamonds(shopItem.getItemValue() * purchaseDTO.getQuantity());
                userService.updateUserDiamonds(userId, user.getDiamonds() + result.getRewardDiamonds());
                break;
            case "item_pack":
                System.out.println("处理道具包: " + shopItem.getItemName() + ", itemValue: " + shopItem.getItemValue());
                processItemPack(userId, shopItem, purchaseDTO.getQuantity());
                result.setRewardItems(shopItem.getItemName());
                break;
            // 新增：直接支持单个道具类型
            case "bomb":
            case "shuffle":
            case "hint":
                int totalItems = shopItem.getItemValue() * purchaseDTO.getQuantity();
                System.out.println("添加单个道具: " + shopItem.getItemType() + ", 数量: " + totalItems);
                userItemService.addUserItem(userId, shopItem.getItemType(), totalItems);
                result.setRewardItems(shopItem.getItemName() + " x" + totalItems);
                break;
            default:
                // 对于其他未知类型，也尝试作为道具处理
                int defaultItems = shopItem.getItemValue() * purchaseDTO.getQuantity();
                System.out.println("处理未知类型道具: " + shopItem.getItemType() + ", 数量: " + defaultItems);
                userItemService.addUserItem(userId, shopItem.getItemType(), defaultItems);
                result.setRewardItems(shopItem.getItemName() + " x" + defaultItems);
                break;
        }

        return result;
    }

    @Override
    public List<PurchaseRecord> getUserPurchaseHistory(Long userId, Integer limit) {
        return purchaseRecordMapper.findRecentByUserId(userId, limit);
    }

    @Override
    public PurchaseRecord getPurchaseByTransactionId(String transactionId) {
        return purchaseRecordMapper.findByTransactionId(transactionId);
    }

    @Override
    public boolean completePurchase(String transactionId) {
        PurchaseRecord record = purchaseRecordMapper.findByTransactionId(transactionId);
        if (record == null || !"pending".equals(record.getStatus())) {
            return false;
        }

        record.setStatus("completed");
        return purchaseRecordMapper.updateById(record) > 0;
    }

    @Override
    public boolean cancelPurchase(String transactionId) {
        PurchaseRecord record = purchaseRecordMapper.findByTransactionId(transactionId);
        if (record == null || !"pending".equals(record.getStatus())) {
            return false;
        }

        record.setStatus("cancelled");
        return purchaseRecordMapper.updateById(record) > 0;
    }

    private void processItemPack(Long userId, ShopItem shopItem, Integer quantity) {
        System.out.println("处理道具包: " + shopItem.getItemName() + ", itemValue: " + shopItem.getItemValue() + ", quantity: " + quantity);
        
        switch (shopItem.getItemName()) {
            case "炸弹道具包":
                // 直接使用itemValue，它表示包含的道具数量
                userItemService.addUserItem(userId, "bomb", shopItem.getItemValue() * quantity);
                System.out.println("添加炸弹道具: " + (shopItem.getItemValue() * quantity) + " 个");
                break;
            case "重排道具包":
                // 直接使用itemValue
                userItemService.addUserItem(userId, "shuffle", shopItem.getItemValue() * quantity);
                System.out.println("添加重排道具: " + (shopItem.getItemValue() * quantity) + " 个");
                break;
            case "提示道具包":
                // 直接使用itemValue
                userItemService.addUserItem(userId, "hint", shopItem.getItemValue() * quantity);
                System.out.println("添加提示道具: " + (shopItem.getItemValue() * quantity) + " 个");
                break;
            case "超值道具包":
                // 根据商品描述：包含3个炸弹、2个重排、5个提示
                // 超值道具包的itemValue在数据库中是0，使用固定值
                userItemService.addUserItem(userId, "bomb", 3 * quantity);
                userItemService.addUserItem(userId, "shuffle", 2 * quantity);
                userItemService.addUserItem(userId, "hint", 5 * quantity);
                System.out.println("添加超值道具包: 炸弹" + (3 * quantity) + "个, 重排" + (2 * quantity) + "个, 提示" + (5 * quantity) + "个");
                break;
            // 保留原有的道具包命名以兼容性
            case "初级道具包":
                userItemService.addUserItem(userId, "bomb", 2 * quantity);
                userItemService.addUserItem(userId, "shuffle", 1 * quantity);
                userItemService.addUserItem(userId, "hint", 3 * quantity);
                break;
            case "中级道具包":
                userItemService.addUserItem(userId, "bomb", 5 * quantity);
                userItemService.addUserItem(userId, "shuffle", 3 * quantity);
                userItemService.addUserItem(userId, "hint", 5 * quantity);
                break;
            case "高级道具包":
                userItemService.addUserItem(userId, "bomb", 10 * quantity);
                userItemService.addUserItem(userId, "shuffle", 5 * quantity);
                userItemService.addUserItem(userId, "hint", 10 * quantity);
                break;
            default:
                // 如果没有匹配的道具包，记录日志
                System.err.println("未知的道具包类型: " + shopItem.getItemName());
                // 尝试使用itemValue作为默认处理
                if (shopItem.getItemValue() > 0) {
                    System.out.println("尝试作为通用道具包处理，添加炸弹道具: " + (shopItem.getItemValue() * quantity) + " 个");
                    userItemService.addUserItem(userId, "bomb", shopItem.getItemValue() * quantity);
                }
                break;
        }
    }
}