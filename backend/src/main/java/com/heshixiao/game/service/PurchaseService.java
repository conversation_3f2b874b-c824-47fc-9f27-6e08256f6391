package com.heshixiao.game.service;

import com.heshixiao.game.dto.PurchaseDTO;
import com.heshixiao.game.entity.PurchaseRecord;
import com.heshixiao.game.vo.PurchaseResultVO;

import java.util.List;

public interface PurchaseService {
    
    PurchaseResultVO purchaseItem(PurchaseDTO purchaseDTO, Long userId);
    
    List<PurchaseRecord> getUserPurchaseHistory(Long userId, Integer limit);
    
    PurchaseRecord getPurchaseByTransactionId(String transactionId);
    
    boolean completePurchase(String transactionId);
    
    boolean cancelPurchase(String transactionId);
}