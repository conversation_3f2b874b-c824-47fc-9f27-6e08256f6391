package com.heshixiao.game.service;

import com.heshixiao.game.vo.LeaderboardVO;

import java.util.List;

public interface LeaderboardService {
    
    List<LeaderboardVO> getGlobalLeaderboard(Integer limit, Long currentUserId);
    
    List<LeaderboardVO> getFriendsLeaderboard(Long userId, Integer limit);
    
    List<LeaderboardVO> getLevelLeaderboard(Integer levelId, Integer limit, Long currentUserId);
    
    Integer getUserRanking(Long userId);
    
    Integer getUserFriendRanking(Long userId);
}