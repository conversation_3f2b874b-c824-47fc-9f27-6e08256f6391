package com.heshixiao.game.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.mapper.UserMapper;
import com.heshixiao.game.service.FriendService;
import com.heshixiao.game.service.LeaderboardService;
import com.heshixiao.game.service.UserProgressService;
import com.heshixiao.game.vo.LeaderboardVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LeaderboardServiceImpl implements LeaderboardService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FriendService friendService;

    @Autowired
    private UserProgressService userProgressService;

    @Override
    public List<LeaderboardVO> getGlobalLeaderboard(Integer limit, Long currentUserId) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("total_score", "max_level")
                   .last("LIMIT " + limit);
        
        List<User> users = userMapper.selectList(queryWrapper);
        List<Long> friendIds = currentUserId != null ? friendService.getFriendIds(currentUserId) : new ArrayList<>();
        
        List<LeaderboardVO> leaderboard = new ArrayList<>();
        for (int i = 0; i < users.size(); i++) {
            User user = users.get(i);
            LeaderboardVO vo = convertToLeaderboardVO(user, i + 1);
            vo.setIsFriend(friendIds.contains(user.getId()));
            leaderboard.add(vo);
        }
        
        return leaderboard;
    }

    @Override
    public List<LeaderboardVO> getFriendsLeaderboard(Long userId, Integer limit) {
        List<Long> friendIds = friendService.getFriendIds(userId);
        if (friendIds.isEmpty()) {
            return new ArrayList<>();
        }

        friendIds.add(userId);

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", friendIds)
                   .orderByDesc("total_score", "max_level")
                   .last("LIMIT " + limit);
        
        List<User> users = userMapper.selectList(queryWrapper);
        List<LeaderboardVO> leaderboard = new ArrayList<>();
        
        for (int i = 0; i < users.size(); i++) {
            User user = users.get(i);
            LeaderboardVO vo = convertToLeaderboardVO(user, i + 1);
            vo.setIsFriend(!user.getId().equals(userId));
            leaderboard.add(vo);
        }
        
        return leaderboard;
    }

    @Override
    public List<LeaderboardVO> getLevelLeaderboard(Integer levelId, Integer limit, Long currentUserId) {
        return new ArrayList<>();
    }

    @Override
    public Integer getUserRanking(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return null;
        }

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("total_score", user.getTotalScore())
                   .or()
                   .eq("total_score", user.getTotalScore())
                   .gt("max_level", user.getMaxLevel());
        
        Long count = userMapper.selectCount(queryWrapper);
        return count.intValue() + 1;
    }

    @Override
    public Integer getUserFriendRanking(Long userId) {
        List<Long> friendIds = friendService.getFriendIds(userId);
        if (friendIds.isEmpty()) {
            return 1;
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            return null;
        }

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", friendIds)
                   .and(wrapper -> wrapper
                       .gt("total_score", user.getTotalScore())
                       .or()
                       .eq("total_score", user.getTotalScore())
                       .gt("max_level", user.getMaxLevel()));
        
        Long count = userMapper.selectCount(queryWrapper);
        return count.intValue() + 1;
    }

    private LeaderboardVO convertToLeaderboardVO(User user, Integer rank) {
        LeaderboardVO vo = new LeaderboardVO();
        vo.setRank(rank);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        vo.setNickname(user.getNickname());
        vo.setAvatarUrl(user.getAvatarUrl());
        vo.setTotalScore(user.getTotalScore());
        
        // 获取用户的实际最高关卡（从用户进度表中查询）
        Integer actualMaxLevel = userProgressService.getUserMaxLevel(user.getId());
        vo.setMaxLevel(actualMaxLevel != null ? actualMaxLevel : 1);
        
        Integer totalStars = userProgressService.getUserTotalStars(user.getId());
        vo.setTotalStars(totalStars != null ? totalStars : 0);
        
        return vo;
    }
}