package com.heshixiao.game.service;

import com.heshixiao.game.dto.UserLoginDTO;
import com.heshixiao.game.dto.UserRegisterDTO;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.vo.LoginResponseVO;
import com.heshixiao.game.vo.UserInfoVO;

public interface UserService {
    
    LoginResponseVO register(UserRegisterDTO userRegisterDTO);
    
    LoginResponseVO login(UserLoginDTO userLoginDTO);
    
    UserInfoVO getUserInfo(Long userId);
    
    User getUserById(Long userId);
    
    boolean updateUserInfo(Long userId, User user);
    
    boolean updateUserCoins(Long userId, Integer coins);
    
    boolean updateUserDiamonds(Long userId, Integer diamonds);
    
    boolean updateUserEnergy(Long userId, Integer energy);
    
    boolean recoverEnergy(Long userId);
    
    boolean checkUsernameExists(String username);
}