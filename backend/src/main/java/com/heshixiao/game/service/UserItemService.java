package com.heshixiao.game.service;

import com.heshixiao.game.dto.UseItemDTO;
import com.heshixiao.game.entity.UserItem;
import com.heshixiao.game.vo.UserItemVO;

import java.util.List;
import java.util.Map;

public interface UserItemService {
    
    List<UserItemVO> getUserItems(Long userId);
    
    UserItem getUserItem(Long userId, String itemType);
    
    boolean addUserItem(Long userId, String itemType, Integer quantity);
    
    boolean useUserItem(Long userId, UseItemDTO useItemDTO);
    
    Integer getUserItemQuantity(Long userId, String itemType);
    
    boolean hasEnoughItems(Long userId, String itemType, Integer requiredQuantity);
    
    // 新增：批量获取用户所有道具数量，提高性能
    Map<String, Integer> getAllUserItemQuantities(Long userId);
}