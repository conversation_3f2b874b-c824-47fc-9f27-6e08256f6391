package com.heshixiao.game.service;

import com.heshixiao.game.entity.UserProgress;

import java.util.List;

public interface UserProgressService {
    
    UserProgress getUserProgress(Long userId, Integer levelId);
    
    List<UserProgress> getUserAllProgress(Long userId);
    
    boolean updateProgress(Long userId, Integer levelId, Integer score, Integer stars, Boolean isCompleted);
    
    Integer getUserMaxLevel(Long userId);
    
    Integer getUserTotalStars(Long userId);
    
    boolean createOrUpdateProgress(UserProgress userProgress);
    
    // 新增：获取用户通关关卡数
    Integer getUserCompletedLevelsCount(Long userId);
    
    // 新增：获取用户详细进度信息
    List<UserProgress> getUserDetailedProgress(Long userId);
}