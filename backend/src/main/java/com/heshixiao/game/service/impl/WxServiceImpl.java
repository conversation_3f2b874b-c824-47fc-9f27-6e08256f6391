package com.heshixiao.game.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heshixiao.game.dto.WxLoginDTO;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.mapper.UserMapper;
import com.heshixiao.game.service.UserService;
import com.heshixiao.game.service.WxService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.LoginResponseVO;
import com.heshixiao.game.vo.UserInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;

@Service
public class WxServiceImpl implements WxService {

    @Value("${wx.appid:}")
    private String appId;

    @Value("${wx.secret:}")
    private String appSecret;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public LoginResponseVO wxLogin(WxLoginDTO wxLoginDTO) {
        try {
            // 1. 通过 code 获取微信用户的 openid 和 session_key
            WxSessionInfo sessionInfo = getWxSession(wxLoginDTO.getCode());
            
            if (sessionInfo == null || sessionInfo.getOpenid() == null) {
                throw new RuntimeException("微信登录失败，请重试");
            }

            // 2. 查找或创建用户
            User user = userMapper.findByOpenid(sessionInfo.getOpenid());
            
            if (user == null) {
                // 创建新用户
                user = createWxUser(sessionInfo, wxLoginDTO.getUserInfo());
            } else {
                // 更新用户信息
                updateWxUser(user, wxLoginDTO.getUserInfo());
            }

            // 3. 生成 JWT token
            String token = jwtUtil.generateToken(user.getId(), user.getUsername());

            // 4. 构造返回值
            LoginResponseVO response = new LoginResponseVO();
            response.setToken(token);
            
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);
            response.setUserInfo(userInfoVO);

            return response;
        } catch (Exception e) {
            System.err.println("微信登录失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("微信登录失败: " + e.getMessage());
        }
    }

    @Override
    public WxSessionInfo getWxSession(String code) {
        try {
            String url = String.format(
                "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                appId, appSecret, code
            );

            System.out.println("请求微信API: " + url);
            String response = restTemplate.getForObject(url, String.class);
            System.out.println("微信API响应: " + response);

            if (response == null) {
                throw new RuntimeException("微信服务器无响应");
            }

            JsonNode jsonNode = objectMapper.readTree(response);
            
            if (jsonNode.has("errcode") && jsonNode.get("errcode").asInt() != 0) {
                String errMsg = jsonNode.has("errmsg") ? jsonNode.get("errmsg").asText() : "未知错误";
                throw new RuntimeException("微信API错误: " + errMsg);
            }

            WxSessionInfo sessionInfo = new WxSessionInfo();
            sessionInfo.setOpenid(jsonNode.get("openid").asText());
            sessionInfo.setSessionKey(jsonNode.get("session_key").asText());
            
            if (jsonNode.has("unionid")) {
                sessionInfo.setUnionid(jsonNode.get("unionid").asText());
            }

            return sessionInfo;
        } catch (Exception e) {
            System.err.println("获取微信会话失败: " + e.getMessage());
            throw new RuntimeException("获取微信会话失败: " + e.getMessage());
        }
    }

    private User createWxUser(WxSessionInfo sessionInfo, WxLoginDTO.WxUserInfo wxUserInfo) {
        User user = new User();
        
        // 设置微信相关信息
        user.setOpenid(sessionInfo.getOpenid());
        user.setUnionid(sessionInfo.getUnionid());
        
        // 设置用户基本信息
        if (wxUserInfo != null) {
            user.setNickname(wxUserInfo.getNickName() != null ? wxUserInfo.getNickName() : "微信用户");
            user.setAvatarUrl(wxUserInfo.getAvatarUrl());
        } else {
            user.setNickname("微信用户");
        }
        
        // 生成唯一用户名
        user.setUsername("wx_" + sessionInfo.getOpenid().substring(0, 8));
        
        // 设置默认值
        user.setCoins(500); // 新用户奖励
        user.setDiamonds(50);
        user.setEnergy(5);
        user.setMaxEnergy(5);
        user.setTotalScore(0L);
        user.setMaxLevel(1);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        // 保存到数据库
        userMapper.insert(user);
        
        System.out.println("创建新微信用户: " + user.getUsername() + ", openid: " + sessionInfo.getOpenid());
        return user;
    }

    private void updateWxUser(User user, WxLoginDTO.WxUserInfo wxUserInfo) {
        boolean needUpdate = false;
        
        if (wxUserInfo != null) {
            if (wxUserInfo.getNickName() != null && !wxUserInfo.getNickName().equals(user.getNickname())) {
                user.setNickname(wxUserInfo.getNickName());
                needUpdate = true;
            }
            
            if (wxUserInfo.getAvatarUrl() != null && !wxUserInfo.getAvatarUrl().equals(user.getAvatarUrl())) {
                user.setAvatarUrl(wxUserInfo.getAvatarUrl());
                needUpdate = true;
            }
        }
        
        if (needUpdate) {
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
            System.out.println("更新微信用户信息: " + user.getUsername());
        }
    }
}