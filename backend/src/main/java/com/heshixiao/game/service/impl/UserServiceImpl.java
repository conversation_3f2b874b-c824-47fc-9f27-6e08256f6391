package com.heshixiao.game.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heshixiao.game.dto.UserLoginDTO;
import com.heshixiao.game.dto.UserRegisterDTO;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.mapper.UserMapper;
import com.heshixiao.game.service.UserService;
import com.heshixiao.game.utils.JwtUtil;
import com.heshixiao.game.vo.LoginResponseVO;
import com.heshixiao.game.vo.UserInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Value("${game.energy.max-default}")
    private Integer defaultMaxEnergy;

    @Value("${game.energy.recovery-interval}")
    private Integer energyRecoveryInterval;

    private BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public LoginResponseVO register(UserRegisterDTO userRegisterDTO) {
        if (checkUsernameExists(userRegisterDTO.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        User user = new User();
        BeanUtils.copyProperties(userRegisterDTO, user);
        
        user.setPassword(passwordEncoder.encode(userRegisterDTO.getPassword()));
        user.setCoins(100);
        user.setDiamonds(10);
        user.setEnergy(defaultMaxEnergy);
        user.setMaxEnergy(defaultMaxEnergy);
        user.setLastEnergyTime(LocalDateTime.now());
        user.setTotalScore(0L);
        user.setMaxLevel(1);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        if (user.getNickname() == null || user.getNickname().trim().isEmpty()) {
            user.setNickname(user.getUsername());
        }

        userMapper.insert(user);

        String token = jwtUtil.generateToken(user.getId(), user.getUsername());
        
        LoginResponseVO response = new LoginResponseVO();
        response.setToken(token);
        response.setUserInfo(convertToUserInfoVO(user));
        
        return response;
    }

    @Override
    public LoginResponseVO login(UserLoginDTO userLoginDTO) {
        User user = userMapper.findByUsername(userLoginDTO.getUsername());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (!passwordEncoder.matches(userLoginDTO.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        recoverEnergy(user.getId());
        user = userMapper.selectById(user.getId());

        String token = jwtUtil.generateToken(user.getId(), user.getUsername());
        
        LoginResponseVO response = new LoginResponseVO();
        response.setToken(token);
        response.setUserInfo(convertToUserInfoVO(user));
        
        return response;
    }

    @Override
    public UserInfoVO getUserInfo(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        recoverEnergy(userId);
        user = userMapper.selectById(userId);
        
        return convertToUserInfoVO(user);
    }

    @Override
    public User getUserById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    public boolean updateUserInfo(Long userId, User user) {
        User existingUser = userMapper.selectById(userId);
        if (existingUser == null) {
            return false;
        }
        
        user.setId(userId);
        user.setUpdatedAt(LocalDateTime.now());
        return userMapper.updateById(user) > 0;
    }

    @Override
    public boolean updateUserCoins(Long userId, Integer coins) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        
        user.setCoins(coins);
        user.setUpdatedAt(LocalDateTime.now());
        return userMapper.updateById(user) > 0;
    }

    @Override
    public boolean updateUserDiamonds(Long userId, Integer diamonds) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        
        user.setDiamonds(diamonds);
        user.setUpdatedAt(LocalDateTime.now());
        return userMapper.updateById(user) > 0;
    }

    @Override
    public boolean updateUserEnergy(Long userId, Integer energy) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        
        user.setEnergy(energy);
        user.setLastEnergyTime(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        return userMapper.updateById(user) > 0;
    }

    @Override
    public boolean recoverEnergy(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null || user.getEnergy() >= user.getMaxEnergy()) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastRecoveryTime = user.getLastEnergyTime();
        
        if (lastRecoveryTime == null) {
            return false;
        }

        long minutesSinceLastRecovery = java.time.Duration.between(lastRecoveryTime, now).toMinutes();
        long energyRecoveryMinutes = energyRecoveryInterval / 60;
        int energyToRecover = (int) (minutesSinceLastRecovery / energyRecoveryMinutes);

        if (energyToRecover > 0) {
            int newEnergy = Math.min(user.getEnergy() + energyToRecover, user.getMaxEnergy());
            user.setEnergy(newEnergy);
            user.setLastEnergyTime(now);
            user.setUpdatedAt(now);
            return userMapper.updateById(user) > 0;
        }

        return false;
    }

    @Override
    public boolean checkUsernameExists(String username) {
        return userMapper.countByUsername(username) > 0;
    }

    private UserInfoVO convertToUserInfoVO(User user) {
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        return userInfoVO;
    }
}