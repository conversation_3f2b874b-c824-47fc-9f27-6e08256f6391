package com.heshixiao.game.service.impl;

import com.heshixiao.game.entity.User;
import com.heshixiao.game.entity.UserFriend;
import com.heshixiao.game.mapper.UserFriendMapper;
import com.heshixiao.game.mapper.UserMapper;
import com.heshixiao.game.service.FriendService;
import com.heshixiao.game.service.UserProgressService;
import com.heshixiao.game.vo.FriendInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class FriendServiceImpl implements FriendService {

    @Autowired
    private UserFriendMapper userFriendMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserProgressService userProgressService;

    @Override
    @Transactional
    public boolean addFriend(Long userId, Long friendId) {
        if (userId.equals(friendId)) {
            throw new RuntimeException("不能添加自己为好友");
        }

        User friend = userMapper.selectById(friendId);
        if (friend == null) {
            throw new RuntimeException("用户不存在");
        }

        if (isFriend(userId, friendId)) {
            throw new RuntimeException("已经是好友关系");
        }

        UserFriend userFriend1 = new UserFriend();
        userFriend1.setUserId(userId);
        userFriend1.setFriendId(friendId);
        userFriend1.setCreatedAt(LocalDateTime.now());

        UserFriend userFriend2 = new UserFriend();
        userFriend2.setUserId(friendId);
        userFriend2.setFriendId(userId);
        userFriend2.setCreatedAt(LocalDateTime.now());

        return userFriendMapper.insert(userFriend1) > 0 && userFriendMapper.insert(userFriend2) > 0;
    }

    @Override
    @Transactional
    public boolean removeFriend(Long userId, Long friendId) {
        UserFriend friendship1 = userFriendMapper.findByUserIdAndFriendId(userId, friendId);
        UserFriend friendship2 = userFriendMapper.findByUserIdAndFriendId(friendId, userId);

        boolean result = true;
        if (friendship1 != null) {
            result = userFriendMapper.deleteById(friendship1.getId()) > 0;
        }
        if (friendship2 != null) {
            result = result && userFriendMapper.deleteById(friendship2.getId()) > 0;
        }

        return result;
    }

    @Override
    public List<FriendInfoVO> getUserFriends(Long userId) {
        List<UserFriend> friendships = userFriendMapper.findByUserId(userId);
        List<FriendInfoVO> friendInfos = new ArrayList<>();

        for (UserFriend friendship : friendships) {
            User friend = userMapper.selectById(friendship.getFriendId());
            if (friend != null) {
                FriendInfoVO friendInfo = new FriendInfoVO();
                friendInfo.setUserId(friend.getId());
                friendInfo.setUsername(friend.getUsername());
                friendInfo.setNickname(friend.getNickname());
                friendInfo.setAvatarUrl(friend.getAvatarUrl());
                friendInfo.setTotalScore(friend.getTotalScore());
                friendInfo.setMaxLevel(friend.getMaxLevel());
                
                Integer totalStars = userProgressService.getUserTotalStars(friend.getId());
                friendInfo.setTotalStars(totalStars != null ? totalStars : 0);
                
                friendInfos.add(friendInfo);
            }
        }

        return friendInfos;
    }

    @Override
    public boolean isFriend(Long userId, Long friendId) {
        UserFriend friendship = userFriendMapper.findByUserIdAndFriendId(userId, friendId);
        return friendship != null;
    }

    @Override
    public Integer getFriendCount(Long userId) {
        return userFriendMapper.countFriendsByUserId(userId);
    }

    @Override
    public List<Long> getFriendIds(Long userId) {
        return userFriendMapper.findFriendIdsByUserId(userId);
    }
}