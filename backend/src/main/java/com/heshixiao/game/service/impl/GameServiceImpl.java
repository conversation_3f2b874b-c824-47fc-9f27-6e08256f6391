package com.heshixiao.game.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heshixiao.game.dto.EndGameDTO;
import com.heshixiao.game.dto.StartGameDTO;
import com.heshixiao.game.entity.GameSession;
import com.heshixiao.game.entity.Level;
import com.heshixiao.game.entity.User;
import com.heshixiao.game.mapper.GameSessionMapper;
import com.heshixiao.game.service.GameService;
import com.heshixiao.game.service.LevelService;
import com.heshixiao.game.service.UserProgressService;
import com.heshixiao.game.service.UserService;
import com.heshixiao.game.vo.GameSessionVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
public class GameServiceImpl implements GameService {

    @Autowired
    private GameSessionMapper gameSessionMapper;

    @Autowired
    private LevelService levelService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserProgressService userProgressService;

    @Override
    @Transactional
    public GameSessionVO startGame(StartGameDTO startGameDTO, Long userId) {
        User user = userService.getUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (user.getEnergy() <= 0) {
            throw new RuntimeException("体力不足，无法开始游戏");
        }

        Level level = levelService.getLevelById(startGameDTO.getLevelId());
        if (level == null) {
            throw new RuntimeException("关卡不存在");
        }

        if (!levelService.isLevelUnlocked(startGameDTO.getLevelId(), userId)) {
            throw new RuntimeException("关卡未解锁");
        }

        userService.updateUserEnergy(userId, user.getEnergy() - 1);

        String sessionId = UUID.randomUUID().toString();
        GameSession gameSession = new GameSession();
        gameSession.setSessionId(sessionId);
        gameSession.setUserId(userId);
        gameSession.setLevelId(startGameDTO.getLevelId());
        gameSession.setScore(0);
        gameSession.setMovesUsed(0);
        gameSession.setTimeUsed(0);
        gameSession.setItemsUsed("[]");
        gameSession.setIsVictory(false);
        gameSession.setStarsEarned(0);
        gameSession.setCreatedAt(LocalDateTime.now());

        gameSessionMapper.insert(gameSession);

        GameSessionVO response = new GameSessionVO();
        BeanUtils.copyProperties(gameSession, response);
        return response;
    }

    @Override
    @Transactional
    public GameSessionVO endGame(EndGameDTO endGameDTO, Long userId) {
        GameSession gameSession = gameSessionMapper.findBySessionId(endGameDTO.getSessionId());
        if (gameSession == null) {
            throw new RuntimeException("游戏会话不存在");
        }

        if (!gameSession.getUserId().equals(userId)) {
            throw new RuntimeException("无权限结束此游戏会话");
        }

        Level level = levelService.getLevelById(gameSession.getLevelId());
        if (level == null) {
            throw new RuntimeException("关卡不存在");
        }

        gameSession.setScore(endGameDTO.getScore());
        gameSession.setMovesUsed(endGameDTO.getMovesUsed());
        gameSession.setTimeUsed(endGameDTO.getTimeUsed());
        gameSession.setItemsUsed(endGameDTO.getItemsUsed());
        gameSession.setIsVictory(endGameDTO.getIsVictory());
        gameSession.setEndReason(endGameDTO.getEndReason());

        Integer stars = calculateStars(endGameDTO.getScore(), level.getStarScores());
        gameSession.setStarsEarned(stars);

        gameSessionMapper.updateById(gameSession);

        if (endGameDTO.getIsVictory()) {
            userProgressService.updateProgress(userId, gameSession.getLevelId(), 
                endGameDTO.getScore(), stars, true);

            User user = userService.getUserById(userId);
            Integer rewardCoins = level.getRewardCoins() != null ? level.getRewardCoins() : 0;
            userService.updateUserCoins(userId, user.getCoins() + rewardCoins);

            Long newTotalScore = user.getTotalScore() + endGameDTO.getScore();
            User updateUser = new User();
            updateUser.setTotalScore(newTotalScore);
            
            Integer currentMaxLevel = userProgressService.getUserMaxLevel(userId);
            if (currentMaxLevel == null || level.getLevelNumber() > currentMaxLevel) {
                updateUser.setMaxLevel(level.getLevelNumber());
            }
            
            userService.updateUserInfo(userId, updateUser);
        } else {
            userProgressService.updateProgress(userId, gameSession.getLevelId(), 
                endGameDTO.getScore(), 0, false);
        }

        GameSessionVO response = new GameSessionVO();
        BeanUtils.copyProperties(gameSession, response);
        response.setRewardCoins(endGameDTO.getIsVictory() ? level.getRewardCoins() : 0);
        return response;
    }

    @Override
    public GameSession getGameSession(String sessionId) {
        return gameSessionMapper.findBySessionId(sessionId);
    }

    @Override
    public List<GameSession> getUserGameHistory(Long userId, Integer limit) {
        return gameSessionMapper.findRecentByUserId(userId, limit);
    }

    @Override
    public boolean validateGameSession(String sessionId, Long userId) {
        GameSession gameSession = gameSessionMapper.findBySessionId(sessionId);
        return gameSession != null && gameSession.getUserId().equals(userId);
    }

    private Integer calculateStars(Integer score, String starScoresJson) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Integer> starScores = objectMapper.readValue(starScoresJson, new TypeReference<List<Integer>>() {});
            if (starScores == null || starScores.size() != 3) {
                return 0;
            }

            if (score >= starScores.get(2)) {
                return 3;
            } else if (score >= starScores.get(1)) {
                return 2;
            } else if (score >= starScores.get(0)) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            return 0;
        }
    }
}