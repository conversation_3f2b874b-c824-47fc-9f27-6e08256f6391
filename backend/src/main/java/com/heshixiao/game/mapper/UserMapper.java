package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);
    
    @Select("SELECT * FROM users WHERE openid = #{openid}")
    User findByOpenid(@Param("openid") String openid);
    
    @Select("SELECT * FROM users WHERE openid = #{wxOpenid}")
    User findByWxOpenid(@Param("wxOpenid") String wxOpenid);
    
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username}")
    int countByUsername(@Param("username") String username);
}