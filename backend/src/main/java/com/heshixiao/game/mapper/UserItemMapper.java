package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.UserItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserItemMapper extends BaseMapper<UserItem> {
    
    @Select("SELECT * FROM user_items WHERE user_id = #{userId} AND item_type = #{itemType}")
    UserItem findByUserIdAndItemType(@Param("userId") Long userId, @Param("itemType") String itemType);
    
    @Select("SELECT * FROM user_items WHERE user_id = #{userId}")
    List<UserItem> findByUserId(@Param("userId") Long userId);
    
    @Select("SELECT SUM(quantity) FROM user_items WHERE user_id = #{userId} AND item_type = #{itemType}")
    Integer sumQuantityByUserIdAndItemType(@Param("userId") Long userId, @Param("itemType") String itemType);
}