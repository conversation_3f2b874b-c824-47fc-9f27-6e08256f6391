package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.UserFriend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserFriendMapper extends BaseMapper<UserFriend> {
    
    @Select("SELECT * FROM user_friends WHERE user_id = #{userId}")
    List<UserFriend> findByUserId(@Param("userId") Long userId);
    
    @Select("SELECT * FROM user_friends WHERE user_id = #{userId} AND friend_id = #{friendId}")
    UserFriend findByUserIdAndFriendId(@Param("userId") Long userId, @Param("friendId") Long friendId);
    
    @Select("SELECT COUNT(*) FROM user_friends WHERE user_id = #{userId}")
    Integer countFriendsByUserId(@Param("userId") Long userId);
    
    @Select("SELECT friend_id FROM user_friends WHERE user_id = #{userId}")
    List<Long> findFriendIdsByUserId(@Param("userId") Long userId);
}