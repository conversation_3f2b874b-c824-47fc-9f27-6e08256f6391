package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.ShopItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ShopItemMapper extends BaseMapper<ShopItem> {
    
    @Select("SELECT * FROM shop_items WHERE is_active = 1 ORDER BY sort_order ASC, id ASC")
    List<ShopItem> findActiveItems();
    
    @Select("SELECT * FROM shop_items WHERE item_type = #{itemType} AND is_active = 1 ORDER BY sort_order ASC")
    List<ShopItem> findActiveItemsByType(@Param("itemType") String itemType);
}