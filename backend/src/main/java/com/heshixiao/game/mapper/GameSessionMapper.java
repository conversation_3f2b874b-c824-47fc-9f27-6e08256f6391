package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.GameSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface GameSessionMapper extends BaseMapper<GameSession> {
    
    @Select("SELECT * FROM game_sessions WHERE session_id = #{sessionId}")
    GameSession findBySessionId(@Param("sessionId") String sessionId);
    
    @Select("SELECT * FROM game_sessions WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT #{limit}")
    List<GameSession> findRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);
    
    @Select("SELECT * FROM game_sessions WHERE user_id = #{userId} AND level_id = #{levelId} ORDER BY created_at DESC")
    List<GameSession> findByUserIdAndLevelId(@Param("userId") Long userId, @Param("levelId") Integer levelId);
    
    @Select("SELECT COUNT(*) FROM game_sessions WHERE user_id = #{userId} AND is_victory = 1")
    Integer countVictoriesByUserId(@Param("userId") Long userId);
}