package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.Level;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface LevelMapper extends BaseMapper<Level> {
    
    @Select("SELECT * FROM levels WHERE level_number = #{levelNumber}")
    Level findByLevelNumber(@Param("levelNumber") Integer levelNumber);
    
    @Select("SELECT * FROM levels ORDER BY level_number ASC")
    List<Level> findAllOrderByLevelNumber();
    
    @Select("SELECT * FROM levels WHERE difficulty = #{difficulty} ORDER BY level_number ASC")
    List<Level> findByDifficulty(@Param("difficulty") Integer difficulty);
}