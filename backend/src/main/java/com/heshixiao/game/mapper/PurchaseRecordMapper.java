package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.PurchaseRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PurchaseRecordMapper extends BaseMapper<PurchaseRecord> {
    
    @Select("SELECT * FROM purchase_records WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT #{limit}")
    List<PurchaseRecord> findRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);
    
    @Select("SELECT * FROM purchase_records WHERE transaction_id = #{transactionId}")
    PurchaseRecord findByTransactionId(@Param("transactionId") String transactionId);
    
    @Select("SELECT COUNT(*) FROM purchase_records WHERE user_id = #{userId} AND status = 'completed'")
    Integer countCompletedPurchasesByUserId(@Param("userId") Long userId);
}