package com.heshixiao.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heshixiao.game.entity.UserProgress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserProgressMapper extends BaseMapper<UserProgress> {
    
    @Select("SELECT * FROM user_progress WHERE user_id = #{userId} AND level_id = #{levelId}")
    UserProgress findByUserIdAndLevelId(@Param("userId") Long userId, @Param("levelId") Integer levelId);
    
    @Select("SELECT * FROM user_progress WHERE user_id = #{userId} ORDER BY level_id ASC")
    List<UserProgress> findByUserId(@Param("userId") Long userId);
    
    // 修正：获取用户完成的最高关卡编号，而不是level_id
    @Select("SELECT MAX(l.level_number) FROM user_progress up " +
            "JOIN levels l ON up.level_id = l.id " +
            "WHERE up.user_id = #{userId} AND up.is_completed = 1")
    Integer findMaxCompletedLevelByUserId(@Param("userId") Long userId);
    
    @Select("SELECT COUNT(*) FROM user_progress WHERE user_id = #{userId} AND stars >= #{stars}")
    Integer countStarsByUserId(@Param("userId") Long userId, @Param("stars") Integer stars);
    
    // 新增：获取用户总的通关关卡数
    @Select("SELECT COUNT(*) FROM user_progress WHERE user_id = #{userId} AND is_completed = 1")
    Integer countCompletedLevels(@Param("userId") Long userId);
    
    // 新增：获取用户进度详情，包含关卡信息
    @Select("SELECT up.*, l.level_number, l.level_name FROM user_progress up " +
            "JOIN levels l ON up.level_id = l.id " +
            "WHERE up.user_id = #{userId} " +
            "ORDER BY l.level_number ASC")
    List<UserProgress> findDetailedProgressByUserId(@Param("userId") Long userId);
}