server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: heshixiao-game-backend
  
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: root
    password: 
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      table-underline: true
  mapper-locations: classpath:mapper/*.xml

logging:
  level:
    com.heshixiao: debug
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

# 游戏配置
game:
  # JWT配置
  jwt:
    secret: heshixiao_game_secret_key_2025_secure_jwt_signing_key_for_authentication
    expiration: 86400  # 24小时
  
  # 体力系统配置
  energy:
    max-default: 5
    recovery-interval: 1800  # 30分钟恢复1点体力
    
  # 棋盘配置
  board:
    width: 8
    height: 8
    number-range-min: 1
    number-range-max: 9

# 微信小程序配置
wx:
  appid: wxc2c72a4019ccb051  # 你的微信小程序 AppID
  secret: eb03d73a8d2588c8374790f98dce5fec  # 你的微信小程序 AppSecret