# 合十消游戏后端项目

基于Spring Boot的《合十消》游戏后端API服务。

## 项目概述

这是一个完整的数字消除类游戏后端系统，提供用户管理、游戏核心功能、道具商城、社交系统等完整的API服务。

## 技术栈

- **Java**: 11
- **Spring Boot**: 2.7.14
- **MyBatis Plus**: 3.5.3.1
- **MySQL**: 8.0+
- **Druid**: 数据库连接池
- **JWT**: 用户认证
- **BCrypt**: 密码加密

## 主要功能模块

### 1. 用户系统
- 用户注册/登录
- 用户信息管理
- 体力系统
- JWT Token认证

### 2. 游戏核心功能
- 关卡管理
- 游戏会话管理
- 进度保存
- 分数统计

### 3. 道具系统
- 道具库存管理
- 道具使用记录
- 炸弹、重排、提示等道具

### 4. 商城系统
- 商品管理
- 购买记录
- 虚拟货币（金币/钻石）
- 道具包销售

### 5. 社交系统
- 好友管理
- 全球排行榜
- 好友排行榜

## API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/info` - 获取用户信息
- `POST /api/user/recover-energy` - 恢复体力

### 游戏相关
- `GET /api/level/list` - 获取关卡列表
- `GET /api/level/{levelId}` - 获取关卡详情
- `POST /api/game/start` - 开始游戏
- `POST /api/game/end` - 结束游戏
- `GET /api/progress/all` - 获取用户进度

### 道具相关
- `GET /api/items/inventory` - 获取道具库存
- `POST /api/items/use` - 使用道具
- `GET /api/shop/items` - 获取商城商品
- `POST /api/purchase/buy` - 购买商品

### 社交相关
- `POST /api/friend/add/{friendId}` - 添加好友
- `GET /api/friend/list` - 获取好友列表
- `GET /api/leaderboard/global` - 全球排行榜
- `GET /api/leaderboard/friends` - 好友排行榜

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: ******************************************
    username: root
    password: 123456
```

### 游戏配置
```yaml
game:
  jwt:
    secret: heshixiao_game_secret_key_2025
    expiration: 86400
  energy:
    max-default: 5
    recovery-interval: 1800
  board:
    width: 8
    height: 8
```

## 启动说明

1. 确保MySQL数据库已安装并运行
2. 执行数据库初始化脚本
3. 修改application.yml中的数据库连接信息
4. 运行项目：
```bash
mvn spring-boot:run
```

## 项目结构

```
backend/
├── src/main/java/com/heshixiao/game/
│   ├── GameBackendApplication.java     # 启动类
│   ├── common/                         # 通用类
│   │   └── Result.java                 # 统一响应格式
│   ├── config/                         # 配置类
│   │   └── CorsConfig.java            # 跨域配置
│   ├── controller/                     # 控制器
│   │   ├── UserController.java        # 用户控制器
│   │   ├── GameController.java        # 游戏控制器
│   │   ├── LevelController.java       # 关卡控制器
│   │   ├── ShopController.java        # 商城控制器
│   │   └── ...
│   ├── entity/                         # 实体类
│   │   ├── User.java                  # 用户实体
│   │   ├── Level.java                 # 关卡实体
│   │   └── ...
│   ├── mapper/                         # MyBatis映射器
│   │   ├── UserMapper.java            # 用户映射器
│   │   └── ...
│   ├── service/                        # 服务接口
│   │   ├── UserService.java           # 用户服务接口
│   │   └── ...
│   ├── service/impl/                   # 服务实现
│   │   ├── UserServiceImpl.java       # 用户服务实现
│   │   └── ...
│   ├── dto/                           # 数据传输对象
│   │   ├── UserLoginDTO.java          # 登录DTO
│   │   └── ...
│   ├── vo/                            # 视图对象
│   │   ├── UserInfoVO.java            # 用户信息VO
│   │   └── ...
│   ├── utils/                         # 工具类
│   │   └── JwtUtil.java               # JWT工具
│   └── exception/                     # 异常处理
│       └── GlobalExceptionHandler.java
└── src/main/resources/
    └── application.yml                 # 配置文件
```

## 数据库依赖

本项目需要使用已经设计好的MySQL数据库，包含以下核心表：
- users（用户表）
- levels（关卡表）
- user_progress（用户进度表）
- game_sessions（游戏会话表）
- user_items（用户道具表）
- shop_items（商城商品表）
- purchase_records（购买记录表）
- user_friends（好友关系表）

请确保在启动项目前已经执行了相应的数据库初始化脚本。